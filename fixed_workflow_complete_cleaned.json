{"name": "My workflow", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "https://drive.google.com/drive/u/2/folders/1LADP6VD4ojtFyjuTNiNwLS3jB25tXG8X", "mode": "url"}, "event": "fileUpdated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-840, 140], "id": "7c988433-4fb7-4c06-aac0-b0575b1b2746", "name": "Google Drive Trigger", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.google-apps.document", "operator": {"type": "string", "operation": "equals"}, "id": "075a29bb-26e8-4d9d-8a3a-3718199ff9cc"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b5703c09-b09f-4fe5-a1c1-c82de4fe23c0", "leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.google-apps.spreadsheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "txt-file-condition", "leftValue": "={{ $json.mimeType }}", "rightValue": "text/plain", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "md-file-condition", "leftValue": "={{ $json.mimeType }}", "rightValue": "text/markdown", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "word-file-condition", "leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "excel-file-condition", "leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-700, 140], "id": "eeeaa849-62b1-4bbf-a211-339a203dfee3", "name": "File Type Filter"}, {"parameters": {"url": "={{ `https://docs.google.com/document/d/${$json.id}/export?format=txt` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-560, 80], "id": "28efce3f-bc85-4369-b7da-f1ef73ed565b", "name": "Export Google Docs as Text", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Google Docs 內容提取\nconst item = $input.item;\n\nconsole.log('📄 Extract Docs Text:');\nconsole.log('  - Input item keys:', Object.keys(item));\n\nlet fileName = 'Unknown Document';\nlet fileId = 'unknown-id';\n\n// 從觸發器獲取文件信息\nif ($('Google Drive Trigger').length > 0) {\n  const triggerData = $('Google Drive Trigger').first().json;\n  fileName = triggerData.name || 'Unknown Document';\n  fileId = triggerData.id || 'unknown-id';\n  console.log('  - 從觸發器獲取文件名:', fileName);\n} else if ($('File Type Filter').length > 0) {\n  const filterData = $('File Type Filter').first().json;\n  fileName = filterData.name || 'Unknown Document';\n  fileId = filterData.id || 'unknown-id';\n  console.log('  - 從過濾器獲取文件名:', fileName);\n}\n\nlet extractedText = '';\n\nif (typeof item.json === 'string') {\n  extractedText = item.json;\n} else if (item.json && typeof item.json.data === 'string') {\n  extractedText = item.json.data;\n} else {\n  console.log('❌ 無法找到文本內容');\n  throw new Error(`Could not find text content in the response`);\n}\n\nif (!extractedText || extractedText.trim().length === 0) {\n  console.log('❌ 提取的文本為空');\n  throw new Error(`Extracted text from Google Doc is empty.`);\n}\n\nconst cleanedText = extractedText\n  .replace(/\\r\\n/g, ' ')\n  .replace(/\\n/g, ' ')\n  .replace(/\\s+/g, ' ')\n  .trim();\n\nconst result = {\n  text: cleanedText,\n  originalFileId: fileId,\n  originalFileName: fileName,\n  fileType: 'docs' // 明確設置為 docs\n};\n\nconsole.log('✅ Docs 文本提取完成:', {\n  fileType: result.fileType,\n  fileName: result.originalFileName,\n  textLength: result.text.length\n});\n\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-420, 80], "id": "fcabddb5-ad4c-4ae1-a20c-07d5fda69d6c", "name": "Extract Docs Text"}, {"parameters": {"url": "={{ `https://sheets.googleapis.com/v4/spreadsheets/${$json.id}/values/A:Z?valueRenderOption=UNFORMATTED_VALUE` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-560, 240], "id": "5894dbd3-6070-4565-858f-f9e680de2c71", "name": "Read Sheet Data", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 處理 Google Sheets 數據\nconst apiResponse = $json;\nlet combinedText = '';\nlet originalDataForRebuild = [];\n\nconsole.log('📊 Format Sheet Data:');\nconsole.log('  - API Response keys:', Object.keys(apiResponse));\n\nif (apiResponse.values && Array.isArray(apiResponse.values)) {\n  const rows = apiResponse.values;\n  console.log('  - 行數:', rows.length);\n  \n  rows.forEach((row, rowIndex) => {\n    originalDataForRebuild.push({\n      rowIndex: rowIndex,\n      values: row || []\n    });\n    \n    if (row && Array.isArray(row)) {\n      row.forEach(cell => {\n        const value = cell ? String(cell).trim() : '';\n        if (value) {\n          combinedText += value + '\\n';\n        }\n      });\n    }\n  });\n} else {\n  console.log('❌ 無效的 Google Sheets API 回應格式');\n  throw new Error('無效的 Google Sheets API 回應格式');\n}\n\nlet originalFileName = 'Unknown Sheet';\nlet fileId = 'unknown-id';\n\n// 從觸發器獲取文件信息\nif ($('Google Drive Trigger').length > 0) {\n  const triggerData = $('Google Drive Trigger').first().json;\n  originalFileName = triggerData.name || 'Unknown Sheet';\n  fileId = triggerData.id || 'unknown-id';\n  console.log('  - 從觸發器獲取文件名:', originalFileName);\n} else if ($('File Type Filter').length > 0) {\n  const filterData = $('File Type Filter').first().json;\n  originalFileName = filterData.name || 'Unknown Sheet';\n  fileId = filterData.id || 'unknown-id';\n  console.log('  - 從過濾器獲取文件名:', originalFileName);\n}\n\nconst result = {\n  text: combinedText.trim(),\n  originalFileId: fileId,\n  originalFileName: originalFileName,\n  fileType: 'sheets', // 明確設置為 sheets\n  originalData: originalDataForRebuild,\n  originalStructure: apiResponse.values\n};\n\nconsole.log('✅ Sheet 數據處理完成:', {\n  fileType: result.fileType,\n  fileName: result.originalFileName,\n  textLength: result.text.length,\n  rowCount: originalDataForRebuild.length\n});\n\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-420, 240], "id": "73914d93-e8d2-4b38-9c20-aab8ffaa1fab", "name": "Format Sheet Data"}, {"parameters": {"jsCode": "// 準備 Google Docs 內容 - 準備檔名API調用\nconst inputData = $json;\nconst fileId = $('Google Drive Trigger').first().json.id;\n\nconsole.log('🔍 Docs Filename Resolver:');\nconsole.log('  - 檔案ID:', fileId);\nconsole.log('  - 輸入數據 fileType:', inputData.fileType);\n\nif (!fileId) {\n  throw new Error('無法獲取檔案ID進行檔名解析');\n}\n\n// 返回數據並準備API調用\nreturn {\n  ...inputData,\n  fileIdForAPI: fileId,\n  apiUrl: `https://www.googleapis.com/drive/v3/files/${fileId}?fields=name`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [940, 0], "id": "6eaf0f4d-c873-40a8-ab20-2ff24fc9a9d8", "name": "Docs Filename Resolver"}, {"parameters": {"url": "={{ $json.apiUrl }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1080, 0], "id": "585887a4-e29e-43a4-8d5d-02d10071615a", "name": "Get Docs Filename API", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 處理 Docs 檔名API響應並準備內容\nconst apiResponse = $json;\nconst docsData = $('Docs Filename Resolver').first().json;\n\nconsole.log('🎯 Process Docs Filename API Response:');\nconsole.log('  - API Response:', JSON.stringify(apiResponse, null, 2));\n\nlet finalFileName = 'DocsAPIFailed';\n\nif (apiResponse.name) {\n  finalFileName = apiResponse.name.replace(/\\.(doc|docx|txt|pdf)$/i, '');\n  console.log('✅ 從API獲取Docs檔名:', finalFileName);\n} else {\n  finalFileName = 'Document_' + new Date().toISOString().substring(0, 10);\n  console.log('❌ API失敗，使用備案檔名:', finalFileName);\n}\n\nconst finalTitle = finalFileName + '_翻譯版';\n\nconsole.log('🏆 最終Docs檔案標題:', finalTitle);\n\nreturn {\n  title: finalTitle,\n  content: docsData.finalTranslation,\n  finalFileName: finalFileName\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1220, 0], "id": "87e149c8-f1ec-400f-9da8-5db79d783156", "name": "Process Docs Filename Response"}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/drive/v3/files", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  name: $json.title || 'Translated Document',\n  parents: ['1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg'],\n  mimeType: 'application/vnd.google-apps.document'\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1360, 0], "id": "958ca64e-a87d-4d85-95aa-8a36ff74c792", "name": "Create Google Doc via Drive API", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Get document info and content\nconst docInfo = $json;\nconst documentId = docInfo.id;\n\nif (!documentId) {\n  throw new Error('Could not find document ID');\n}\n\nconst prepNodeItems = $('Process Docs Filename Response').all();\n\nif (!prepNodeItems || prepNodeItems.length === 0) {\n    throw new Error('Could not find output from the \"Process Docs Filename Response\" node.');\n}\n\nconst translationContent = prepNodeItems[0].json.content;\n\nif (!translationContent) {\n  throw new Error('Could not find \"content\" in the output of \"Process Docs Filename Response\" node.');\n}\n\nreturn {\n  documentId: documentId,\n  content: translationContent,\n  documentUrl: `https://docs.google.com/document/d/${documentId}/edit`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1500, 0], "id": "46ae9945-5249-4951-8c17-a4ccf52b270a", "name": "Prepare Final Payload"}, {"parameters": {"method": "POST", "url": "={{ `https://docs.googleapis.com/v1/documents/${$json.documentId}:batchUpdate` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDocsOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  requests: [\n    {\n      insertText: {\n        location: {\n          index: 1\n        },\n        text: $json.content || '無法獲取翻譯內容'\n      }\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1640, 0], "id": "e9990cf5-9922-4950-aade-4164e68c7718", "name": "Add Content to Google Doc", "credentials": {"googleDocsOAuth2Api": {"id": "GtMyeGieoILXwpLs", "name": "Google Docs account"}}}, {"parameters": {"jsCode": "// Rebuild Translated Sheet Data\nconst inputData = $json;\n\nconsole.log('🔧 Rebuild Translated Sheet Data:');\nconsole.log('  - 輸入數據:', JSON.stringify(inputData, null, 2));\n\n// 獲取翻譯文本\nlet translatedText = inputData.finalTranslation;\nif (!translatedText) {\n  console.log('❌ 找不到 finalTranslation，檢查其他可能的字段...');\n  translatedText = inputData.translatedText || inputData.content || '';\n}\n\nconsole.log('📝 翻譯文本長度:', translatedText.length);\n\n// 獲取原始結構\nlet originalStructure = inputData.originalStructure;\n\nif (!originalStructure) {\n  console.log('🔍 originalStructure 不存在，嘗試從其他節點獲取...');\n  \n  // 嘗試從 Format Sheet Data 節點獲取\n  try {\n    if ($('Format Sheet Data').length > 0) {\n      const formatData = $('Format Sheet Data').first().json;\n      originalStructure = formatData.originalStructure;\n      console.log('✅ 從 Format Sheet Data 獲取到結構');\n    }\n  } catch (error) {\n    console.log('⚠️ 無法從 Format Sheet Data 獲取結構:', error.message);\n  }\n}\n\nif (!originalStructure) {\n  console.log('🆕 創建默認結構...');\n  const lines = translatedText.split('\\n').filter(line => line.trim() !== '');\n  originalStructure = lines.map(line => [line]);\n  console.log('📊 創建了', originalStructure.length, '行的默認結構');\n}\n\n// 重建數據\nconst translatedLines = translatedText.split('\\n').filter(line => line.trim() !== '');\nlet lineIndex = 0;\nconst rebuiltValues = [];\n\nconsole.log('🔄 開始重建數據...');\nconsole.log('  - 翻譯行數:', translatedLines.length);\nconsole.log('  - 原始結構行數:', originalStructure.length);\n\nfor (let rowIndex = 0; rowIndex < originalStructure.length; rowIndex++) {\n  const originalRow = originalStructure[rowIndex] || [];\n  const newRow = [];\n  \n  for (let colIndex = 0; colIndex < originalRow.length; colIndex++) {\n    if (lineIndex < translatedLines.length) {\n      newRow.push(translatedLines[lineIndex].trim());\n      lineIndex++;\n    } else {\n      newRow.push(originalRow[colIndex] || '');\n    }\n  }\n  \n  rebuiltValues.push(newRow);\n}\n\nconsole.log('✅ 重建完成，總行數:', rebuiltValues.length);\n\nreturn {\n  values: rebuiltValues,\n  range: 'A:Z',\n  majorDimension: 'ROWS'\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [940, 260], "id": "09ecf4a5-8848-4570-a36d-50b8abe9e95c", "name": "Rebuild Translated Sheet Data"}, {"parameters": {"jsCode": "// 獲取檔案ID用於實時檔名解析\nconst inputData = $json;\nconst fileId = $('Google Drive Trigger').first().json.id;\n\nconsole.log('🔍 Real-time Filename Resolver:');\nconsole.log('  - 檔案ID:', fileId);\n\nif (!fileId) {\n  throw new Error('無法獲取檔案ID進行檔名解析');\n}\n\n// 返回數據並準備API調用\nreturn {\n  ...inputData,\n  fileIdForAPI: fileId,\n  apiUrl: `https://www.googleapis.com/drive/v3/files/${fileId}?fields=name`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1080, 260], "id": "d60b5a6d-0e1c-41a2-8a1a-82d55836b754", "name": "Filename Resolver"}, {"parameters": {"url": "={{ $json.apiUrl }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1220, 260], "id": "2989e32d-7cdb-4717-9a25-b50c9076eb2a", "name": "Get Filename API", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 處理檔名API響應並創建最終標題\nconst apiResponse = $json;\nconst rebuiltData = $('Rebuild Translated Sheet Data').first().json;\n\nconsole.log('🎯 Process Filename API Response:');\nconsole.log('  - API Response:', JSON.stringify(apiResponse, null, 2));\n\nlet finalFileName = 'APIFailed';\n\nif (apiResponse.name) {\n  finalFileName = apiResponse.name.replace(/\\.(xlsx?|csv|ods|gsheet)$/i, '');\n  console.log('✅ 從API獲取檔名:', finalFileName);\n} else {\n  finalFileName = 'Sheet_' + new Date().toISOString().substring(0, 10);\n  console.log('❌ API失敗，使用備案檔名:', finalFileName);\n}\n\nconst finalTitle = finalFileName + '_翻譯版';\n\nconsole.log('🏆 最終檔案標題:', finalTitle);\n\nreturn {\n  ...rebuiltData,\n  finalSheetTitle: finalTitle,\n  finalFileName: finalFileName\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1360, 260], "id": "9aeb5750-38dd-4887-9b7a-a47f48807f6b", "name": "Process Filename Response"}, {"parameters": {"method": "POST", "url": "https://sheets.googleapis.com/v4/spreadsheets", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  properties: {\n    title: $json.finalSheetTitle || 'Fallback_翻譯版'\n  },\n  sheets: [{\n    properties: {\n      title: '翻譯結果',\n      gridProperties: {\n        rowCount: 1000,\n        columnCount: 26\n      }\n    }\n  }]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1500, 260], "id": "177c8d96-e569-4405-b4ea-70c39c921d10", "name": "Create Translated Sheet", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 準備Sheet內容寫入\nconst createResponse = $json;\nconst processedData = $('Process Filename Response').first().json;\n\nconst newSpreadsheetId = createResponse.spreadsheetId;\n\nif (!newSpreadsheetId) {\n  throw new Error(\"Could not get new spreadsheet ID from create response.\");\n}\n\nconst valuesToWrite = processedData.values || [];\n\nreturn {\n  spreadsheetId: newSpreadsheetId,\n  values: valuesToWrite,\n  range: '翻譯結果!A:Z',\n  majorDimension: 'ROWS',\n  newFileUrl: `https://docs.google.com/spreadsheets/d/${newSpreadsheetId}/edit`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1640, 260], "id": "87f7281f-3d7f-4d2d-8eec-45c4bce773a5", "name": "Prepare Sheet Write"}, {"parameters": {"method": "PATCH", "url": "={{ `https://www.googleapis.com/drive/v3/files/${$json.spreadsheetId}` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendQuery": true, "queryParameters": {"parameters": [{"name": "addParents", "value": "1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg"}, {"name": "fields", "value": "id,parents"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1780, 260], "id": "1787696e-da0e-4ba0-8deb-21e0e3e2522b", "name": "Move to Output Folder", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 重建寫入Payload\nconst moveResponse = $json;\nconst prepareData = $('Prepare Sheet Write').first().json;\n\nreturn {\n  spreadsheetId: prepareData.spreadsheetId,\n  values: prepareData.values,\n  range: prepareData.range,\n  majorDimension: prepareData.majorDimension,\n  newFileUrl: prepareData.newFileUrl\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1920, 260], "id": "09c82918-da8a-4ae9-b27c-5b818c0a1e13", "name": "Rebuild Write Payload"}, {"parameters": {"method": "PUT", "url": "={{ `https://sheets.googleapis.com/v4/spreadsheets/${$json.spreadsheetId}/values/${encodeURIComponent($json.range)}?valueInputOption=RAW` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  values: $json.values,\n  majorDimension: 'ROWS'\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2060, 260], "id": "423f9131-310d-4b8b-a4c2-2bb8f87b4877", "name": "Write to Sheet", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=AIzaSyD5mbvRs_vIuAE9twS2wU-IpK_RMMHV9oo", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  contents: [{\n    parts: [{\n      text: `You are a professional translation engine specializing in English to Traditional Chinese (Taiwan) translation. Your task:\n\n1. Translate the following text into Traditional Chinese suitable for Taiwan readers\n2. Preserve the EXACT line structure - if input has N lines, output must have N lines\n3. Use natural, fluent Chinese expressions while maintaining accuracy\n4. Keep professional terminology precise\n5. Do not add explanations or introductory text - only provide the translation\n\nText to translate:\n---\n${$json.text || 'No text content found'}`\n    }]\n  }]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, 0], "id": "3617bad6-58d4-405a-bd5e-101d1cce433e", "name": "First Translation (Gemini) - Docs"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=AIzaSyD5mbvRs_vIuAE9twS2wU-IpK_RMMHV9oo", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  contents: [{\n    parts: [{\n      text: `You are a professional translation engine specializing in English to Traditional Chinese (Taiwan) translation. Your task:\n\n1. Translate the following text into Traditional Chinese suitable for Taiwan readers\n2. Preserve the EXACT line structure - if input has N lines, output must have N lines\n3. Use natural, fluent Chinese expressions while maintaining accuracy\n4. Keep professional terminology precise\n5. Do not add explanations or introductory text - only provide the translation\n\nText to translate:\n---\n${$json.text || 'No text content found'}`\n    }]\n  }]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, 260], "id": "4046d01c-4d1e-4888-b3ba-7dfd43fabe12", "name": "First Translation (Gemini) - Sheets"}, {"parameters": {"jsCode": "// 處理 Docs Gemini API 回應\nconst geminiResponse = $json;\n\nlet sourceData = { originalFileName: 'Unknown', fileType: 'docs', originalData: null };\nif ($('Extract Docs Text').length > 0) {\n  sourceData = $('Extract Docs Text').first().json;\n}\n\nlet translatedText = 'Translation failed';\nif (geminiResponse.candidates && geminiResponse.candidates[0] && geminiResponse.candidates[0].content && geminiResponse.candidates[0].content.parts && geminiResponse.candidates[0].content.parts[0]) {\n  translatedText = geminiResponse.candidates[0].content.parts[0].text;\n}\n\nconsole.log('🔍 Process Docs Gemini Response:');\nconsole.log('  - fileType:', sourceData.fileType);\nconsole.log('  - fileName:', sourceData.originalFileName);\nconsole.log('  - 翻譯長度:', translatedText ? translatedText.length : 0);\n\nreturn {\n  initialTranslation: translatedText,\n  originalFileName: sourceData.originalFileName,\n  fileType: sourceData.fileType,\n  originalData: sourceData.originalData,\n  originalStructure: sourceData.originalStructure\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [200, 0], "id": "c5d13a88-94fa-4d9c-a3db-989ef8efc36c", "name": "Process Docs Gemini Response"}, {"parameters": {"jsCode": "// 處理 Sheets Gemini API 回應\nconst geminiResponse = $json;\n\nconsole.log('🔍 Process Sheets Gemini Response:');\nconsole.log('  - Gemini 回應:', JSON.stringify(geminiResponse, null, 2));\n\nlet sourceData = { originalFileName: 'Unknown', fileType: 'sheets', originalData: null, text: '' };\nif ($('Format Sheet Data').length > 0) {\n  sourceData = $('Format Sheet Data').first().json;\n  console.log('  - 從 Format Sheet Data 獲取源數據:', JSON.stringify(sourceData, null, 2));\n}\n\nlet translatedText = 'Translation failed';\n\n// 檢查 Gemini 回應\nif (geminiResponse && geminiResponse.candidates && geminiResponse.candidates[0] && geminiResponse.candidates[0].content && geminiResponse.candidates[0].content.parts && geminiResponse.candidates[0].content.parts[0]) {\n  translatedText = geminiResponse.candidates[0].content.parts[0].text;\n  console.log('✅ 成功從 Gemini 獲取翻譯');\n} else {\n  console.log('⚠️ Gemini 翻譯失敗，使用原始文本作為備案');\n  // 如果 Gemini 失敗，使用原始文本\n  translatedText = sourceData.text || 'No text available';\n}\n\nconsole.log('📊 最終結果:');\nconsole.log('  - fileType:', sourceData.fileType);\nconsole.log('  - fileName:', sourceData.originalFileName);\nconsole.log('  - 翻譯長度:', translatedText ? translatedText.length : 0);\n\nreturn {\n  initialTranslation: translatedText,\n  originalFileName: sourceData.originalFileName,\n  fileType: sourceData.fileType,\n  originalData: sourceData.originalData,\n  originalStructure: sourceData.originalStructure\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [200, 260], "id": "70aa4951-47ff-4c47-b3df-9abcbc1793ea", "name": "Process Sheets Gemini Response"}, {"parameters": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "************************************************************************************************************"}, {"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  \"model\": \"claude-3-5-sonnet-20240620\",\n  \"max_tokens\": 4096,\n  \"system\": \"你是一位資深的翻譯審校專家。你收到的內容是一段初步的中文翻譯。你的任務是進行以下改進：\\n1. **語意準確性**：確保翻譯完全傳達原文含義。\\n2. **語言自然性**：改善中文表達的流暢度和自然性，使其符合台灣讀者習慣。\\n\\n**最重要的規則是：** 輸入的文本可能包含多個由換行符分隔的獨立片段。你必須**嚴格保持原始的換行符結構**。如果輸入的某一行是空的，輸出的對應行也必須是空的。輸出的總行數必須與輸入的總行數完全相同。絕不添加任何額外的解釋或開場白，只需提供最終的、逐行對應的翻譯結果。\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": `初始中文翻譯：\\n---\\n${$json.initialTranslation}\\n---\\n\\n請提供改進後的翻譯版本。`\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, 0], "id": "d507bcd4-9267-411c-b828-fbc30ebff94a", "name": "Reflective Translation (Claude) - Docs"}, {"parameters": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "************************************************************************************************************"}, {"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  \"model\": \"claude-3-5-sonnet-20240620\",\n  \"max_tokens\": 4096,\n  \"system\": \"你是一位資深的翻譯審校專家。你收到的內容是一段初步的中文翻譯。你的任務是進行以下改進：\\n1. **語意準確性**：確保翻譯完全傳達原文含義。\\n2. **語言自然性**：改善中文表達的流暢度和自然性，使其符合台灣讀者習慣。\\n\\n**最重要的規則是：** 輸入的文本可能包含多個由換行符分隔的獨立片段。你必須**嚴格保持原始的換行符結構**。如果輸入的某一行是空的，輸出的對應行也必須是空的。輸出的總行數必須與輸入的總行數完全相同。絕不添加任何額外的解釋或開場白，只需提供最終的、逐行對應的翻譯結果。\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": `初始中文翻譯：\\n---\\n${$json.initialTranslation}\\n---\\n\\n請提供改進後的翻譯版本。`\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, 260], "id": "4feab357-75c3-4e82-908e-c490a604f5d7", "name": "Reflective Translation (<PERSON>) - Sheets"}, {"parameters": {"jsCode": "// 解析 Docs Claude API 回應\nconst claudeResponse = $json;\n\nlet upstreamData = { \n  initialTranslation: 'No translation', \n  originalFileName: 'Unknown', \n  fileType: 'docs', \n  originalData: null, \n  originalStructure: null \n};\n\nif ($('Process Docs Gemini Response').length > 0) {\n  upstreamData = $('Process Docs Gemini Response').first().json;\n}\n\nlet finalTranslation = upstreamData.initialTranslation;\nif (claudeResponse.content && claudeResponse.content[0] && claudeResponse.content[0].text) {\n  finalTranslation = claudeResponse.content[0].text;\n}\n\nconsole.log('🏆 Process Docs Claude Response:');\nconsole.log('  - fileType:', upstreamData.fileType);\nconsole.log('  - fileName:', upstreamData.originalFileName);\nconsole.log('  - 最終翻譯長度:', finalTranslation ? finalTranslation.length : 0);\n\nreturn {\n  finalTranslation: finalTranslation.trim(),\n  originalFileName: upstreamData.originalFileName,\n  fileType: upstreamData.fileType,\n  originalData: upstreamData.originalData,\n  originalStructure: upstreamData.originalStructure\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [600, 0], "id": "25e8846f-4a17-4292-b1a9-2b291c57b347", "name": "Process Docs Claude Response"}, {"parameters": {"jsCode": "// 解析 Sheets Claude API 回應\nconst claudeResponse = $json;\n\nlet upstreamData = { \n  initialTranslation: 'No translation', \n  originalFileName: 'Unknown', \n  fileType: 'sheets', \n  originalData: null, \n  originalStructure: null \n};\n\nif ($('Process Sheets Gemini Response').length > 0) {\n  upstreamData = $('Process Sheets Gemini Response').first().json;\n}\n\nlet finalTranslation = upstreamData.initialTranslation;\nif (claudeResponse.content && claudeResponse.content[0] && claudeResponse.content[0].text) {\n  finalTranslation = claudeResponse.content[0].text;\n}\n\nconsole.log('🏆 Process Sheets Claude Response:');\nconsole.log('  - fileType:', upstreamData.fileType);\nconsole.log('  - fileName:', upstreamData.originalFileName);\nconsole.log('  - 最終翻譯長度:', finalTranslation ? finalTranslation.length : 0);\n\nreturn {\n  finalTranslation: finalTranslation.trim(),\n  originalFileName: upstreamData.originalFileName,\n  fileType: upstreamData.fileType,\n  originalData: upstreamData.originalData,\n  originalStructure: upstreamData.originalStructure\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [600, 260], "id": "d4d5762f-8dd8-4e87-b56e-f309bf0a2a52", "name": "Process Sheets Claude Response"}, {"parameters": {"url": "={{ `https://drive.google.com/uc?export=download&id=${$json.id}` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-560, 400], "id": "txt-download-node", "name": "Download TXT File", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// TXT 檔案內容提取\nconst item = $input.item;\n\nconsole.log('📄 Extract TXT Text:');\nconsole.log('  - Input item keys:', Object.keys(item));\n\nlet fileName = 'Unknown TXT Document';\nlet fileId = 'unknown-id';\n\n// 從觸發器獲取文件信息\nif ($('Google Drive Trigger').length > 0) {\n  const triggerData = $('Google Drive Trigger').first().json;\n  fileName = triggerData.name || 'Unknown TXT Document';\n  fileId = triggerData.id || 'unknown-id';\n  console.log('  - 從觸發器獲取文件名:', fileName);\n} else if ($('File Type Filter').length > 0) {\n  const filterData = $('File Type Filter').first().json;\n  fileName = filterData.name || 'Unknown TXT Document';\n  fileId = filterData.id || 'unknown-id';\n  console.log('  - 從過濾器獲取文件名:', fileName);\n}\n\nlet extractedText = '';\n\nif (typeof item.json === 'string') {\n  extractedText = item.json;\n} else if (item.json && typeof item.json.data === 'string') {\n  extractedText = item.json.data;\n} else if (item.data && typeof item.data === 'string') {\n  extractedText = item.data;\n} else {\n  console.log('❌ 無法找到文本內容');\n  throw new Error(`Could not find text content in the TXT file response`);\n}\n\nif (!extractedText || extractedText.trim().length === 0) {\n  console.log('❌ 提取的文本為空');\n  throw new Error(`Extracted text from TXT file is empty.`);\n}\n\nconst cleanedText = extractedText\n  .replace(/\\r\\n/g, '\\n')\n  .replace(/\\r/g, '\\n')\n  .trim();\n\nconst result = {\n  text: cleanedText,\n  originalFileId: fileId,\n  originalFileName: fileName,\n  fileType: 'txt' // 明確設置為 txt\n};\n\nconsole.log('✅ TXT 文本提取完成:', {\n  fileType: result.fileType,\n  fileName: result.originalFileName,\n  textLength: result.text.length\n});\n\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-420, 400], "id": "txt-extract-node", "name": "Extract TXT Text"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=AIzaSyD5mbvRs_vIuAE9twS2wU-IpK_RMMHV9oo", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  contents: [{\n    parts: [{\n      text: `You are a professional translation engine specializing in English to Traditional Chinese (Taiwan) translation. Your task:\n\n1. Translate the following text into Traditional Chinese suitable for Taiwan readers\n2. Preserve the EXACT line structure - if input has N lines, output must have N lines\n3. Use natural, fluent Chinese expressions while maintaining accuracy\n4. Keep professional terminology precise\n5. Do not add explanations or introductory text - only provide the translation\n\nText to translate:\n---\n${$json.text || 'No text content found'}`\n    }]\n  }]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, 400], "id": "txt-gemini-translation", "name": "First Translation (Gemini) - TXT"}, {"parameters": {"jsCode": "// 處理 TXT Gemini API 回應\nconst geminiResponse = $json;\n\nlet sourceData = { originalFileName: 'Unknown', fileType: 'txt', originalData: null };\nif ($('Extract TXT Text').length > 0) {\n  sourceData = $('Extract TXT Text').first().json;\n}\n\nlet translatedText = 'Translation failed';\nif (geminiResponse.candidates && geminiResponse.candidates[0] && geminiResponse.candidates[0].content && geminiResponse.candidates[0].content.parts && geminiResponse.candidates[0].content.parts[0]) {\n  translatedText = geminiResponse.candidates[0].content.parts[0].text;\n}\n\nconsole.log('🔍 Process TXT Gemini Response:');\nconsole.log('  - fileType:', sourceData.fileType);\nconsole.log('  - fileName:', sourceData.originalFileName);\nconsole.log('  - 翻譯長度:', translatedText ? translatedText.length : 0);\n\nreturn {\n  initialTranslation: translatedText,\n  originalFileName: sourceData.originalFileName,\n  fileType: sourceData.fileType,\n  originalData: sourceData.originalData,\n  originalStructure: sourceData.originalStructure\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [200, 400], "id": "txt-process-gemini", "name": "Process TXT Gemini Response"}, {"parameters": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "************************************************************************************************************"}, {"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  \"model\": \"claude-3-5-sonnet-20240620\",\n  \"max_tokens\": 4096,\n  \"system\": \"你是一位資深的翻譯審校專家。你收到的內容是一段初步的中文翻譯。你的任務是進行以下改進：\\n1. **語意準確性**：確保翻譯完全傳達原文含義。\\n2. **語言自然性**：改善中文表達的流暢度和自然性，使其符合台灣讀者習慣。\\n\\n**最重要的規則是：** 輸入的文本可能包含多個由換行符分隔的獨立片段。你必須**嚴格保持原始的換行符結構**。如果輸入的某一行是空的，輸出的對應行也必須是空的。輸出的總行數必須與輸入的總行數完全相同。絕不添加任何額外的解釋或開場白，只需提供最終的、逐行對應的翻譯結果。\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": `初始中文翻譯：\\n---\\n${$json.initialTranslation}\\n---\\n\\n請提供改進後的翻譯版本。`\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, 400], "id": "txt-claude-translation", "name": "Reflective Translation (Claude) - TXT"}, {"parameters": {"jsCode": "// 解析 TXT Claude API 回應\nconst claudeResponse = $json;\n\nlet upstreamData = { \n  initialTranslation: 'No translation', \n  originalFileName: 'Unknown', \n  fileType: 'txt', \n  originalData: null, \n  originalStructure: null \n};\n\nif ($('Process TXT Gemini Response').length > 0) {\n  upstreamData = $('Process TXT Gemini Response').first().json;\n}\n\nlet finalTranslation = upstreamData.initialTranslation;\nif (claudeResponse.content && claudeResponse.content[0] && claudeResponse.content[0].text) {\n  finalTranslation = claudeResponse.content[0].text;\n}\n\nconsole.log('🏆 Process TXT Claude Response:');\nconsole.log('  - fileType:', upstreamData.fileType);\nconsole.log('  - fileName:', upstreamData.originalFileName);\nconsole.log('  - 最終翻譯長度:', finalTranslation ? finalTranslation.length : 0);\n\nreturn {\n  finalTranslation: finalTranslation.trim(),\n  originalFileName: upstreamData.originalFileName,\n  fileType: upstreamData.fileType,\n  originalData: upstreamData.originalData,\n  originalStructure: upstreamData.originalStructure\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [600, 400], "id": "txt-process-claude", "name": "Process TXT Claude Response"}, {"parameters": {"jsCode": "// 準備 TXT 內容 - 準備檔名API調用\nconst inputData = $json;\nconst fileId = $('Google Drive Trigger').first().json.id;\n\nconsole.log('🔍 TXT Filename Resolver:');\nconsole.log('  - 檔案ID:', fileId);\nconsole.log('  - 輸入數據 fileType:', inputData.fileType);\n\nif (!fileId) {\n  throw new Error('無法獲取檔案ID進行檔名解析');\n}\n\n// 返回數據並準備API調用\nreturn {\n  ...inputData,\n  fileIdForAPI: fileId,\n  apiUrl: `https://www.googleapis.com/drive/v3/files/${fileId}?fields=name`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [940, 400], "id": "txt-filename-resolver", "name": "TXT Filename Resolver"}, {"parameters": {"url": "={{ $json.apiUrl }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1080, 400], "id": "txt-get-filename-api", "name": "Get TXT Filename API", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 處理 TXT 檔名API響應並準備內容\nconst apiResponse = $json;\nconst txtData = $('TXT Filename Resolver').first().json;\n\nconsole.log('🎯 Process TXT Filename API Response:');\nconsole.log('  - API Response:', JSON.stringify(apiResponse, null, 2));\n\nlet finalFileName = 'TXTAPIFailed';\n\nif (apiResponse.name) {\n  finalFileName = apiResponse.name.replace(/\\.(txt|text)$/i, '');\n  console.log('✅ 從API獲取TXT檔名:', finalFileName);\n} else {\n  finalFileName = 'TXT_Document_' + new Date().toISOString().substring(0, 10);\n  console.log('❌ API失敗，使用備案檔名:', finalFileName);\n}\n\nconst finalTitle = finalFileName + '_翻譯版';\n\nconsole.log('🏆 最終TXT檔案標題:', finalTitle);\n\nreturn {\n  title: finalTitle,\n  content: txtData.finalTranslation,\n  finalFileName: finalFileName\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1220, 400], "id": "txt-process-filename-response", "name": "Process TXT Filename Response"}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/drive/v3/files", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  name: $json.title || 'Translated TXT Document',\n  parents: ['1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg'],\n  mimeType: 'application/vnd.google-apps.document'\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1360, 400], "id": "txt-create-google-doc", "name": "Create Google Doc for TXT", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Get document info and content for TXT\nconst docInfo = $json;\nconst documentId = docInfo.id;\n\nif (!documentId) {\n  throw new Error('Could not find document ID');\n}\n\nconst prepNodeItems = $('Process TXT Filename Response').all();\n\nif (!prepNodeItems || prepNodeItems.length === 0) {\n    throw new Error('Could not find output from the \"Process TXT Filename Response\" node.');\n}\n\nconst translationContent = prepNodeItems[0].json.content;\n\nif (!translationContent) {\n  throw new Error('Could not find \"content\" in the output of \"Process TXT Filename Response\" node.');\n}\n\nreturn {\n  documentId: documentId,\n  content: translationContent,\n  documentUrl: `https://docs.google.com/document/d/${documentId}/edit`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1500, 400], "id": "txt-prepare-final-payload", "name": "Prepare TXT Final Payload"}, {"parameters": {"method": "POST", "url": "={{ `https://docs.googleapis.com/v1/documents/${$json.documentId}:batchUpdate` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDocsOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  requests: [\n    {\n      insertText: {\n        location: {\n          index: 1\n        },\n        text: $json.content || '無法獲取翻譯內容'\n      }\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1640, 400], "id": "txt-add-content-to-doc", "name": "Add Content to TXT Google Doc", "credentials": {"googleDocsOAuth2Api": {"id": "GtMyeGieoILXwpLs", "name": "Google Docs account"}}}, {"parameters": {"url": "={{ `https://drive.google.com/uc?export=download&id=${$json.id}` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-560, 560], "id": "md-download-node", "name": "Download MD File", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// MD 檔案內容提取\nconst item = $input.item;\n\nconsole.log('📄 Extract MD Text:');\nconsole.log('  - Input item keys:', Object.keys(item));\n\nlet fileName = 'Unknown MD Document';\nlet fileId = 'unknown-id';\n\n// 從觸發器獲取文件信息\nif ($('Google Drive Trigger').length > 0) {\n  const triggerData = $('Google Drive Trigger').first().json;\n  fileName = triggerData.name || 'Unknown MD Document';\n  fileId = triggerData.id || 'unknown-id';\n  console.log('  - 從觸發器獲取文件名:', fileName);\n} else if ($('File Type Filter').length > 0) {\n  const filterData = $('File Type Filter').first().json;\n  fileName = filterData.name || 'Unknown MD Document';\n  fileId = filterData.id || 'unknown-id';\n  console.log('  - 從過濾器獲取文件名:', fileName);\n}\n\nlet extractedText = '';\n\nif (typeof item.json === 'string') {\n  extractedText = item.json;\n} else if (item.json && typeof item.json.data === 'string') {\n  extractedText = item.json.data;\n} else if (item.data && typeof item.data === 'string') {\n  extractedText = item.data;\n} else {\n  console.log('❌ 無法找到文本內容');\n  throw new Error(`Could not find text content in the MD file response`);\n}\n\nif (!extractedText || extractedText.trim().length === 0) {\n  console.log('❌ 提取的文本為空');\n  throw new Error(`Extracted text from MD file is empty.`);\n}\n\n// 保持 Markdown 格式，不做過多清理\nconst cleanedText = extractedText\n  .replace(/\\r\\n/g, '\\n')\n  .replace(/\\r/g, '\\n')\n  .trim();\n\nconst result = {\n  text: cleanedText,\n  originalFileId: fileId,\n  originalFileName: fileName,\n  fileType: 'md' // 明確設置為 md\n};\n\nconsole.log('✅ MD 文本提取完成:', {\n  fileType: result.fileType,\n  fileName: result.originalFileName,\n  textLength: result.text.length\n});\n\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-420, 560], "id": "md-extract-node", "name": "Extract MD Text"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=AIzaSyD5mbvRs_vIuAE9twS2wU-IpK_RMMHV9oo", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  contents: [{\n    parts: [{\n      text: `You are a professional translation engine specializing in English to Traditional Chinese (Taiwan) translation for Markdown documents. Your task:\n\n1. Translate the following Markdown content into Traditional Chinese suitable for Taiwan readers\n2. PRESERVE ALL Markdown formatting (headers, links, code blocks, lists, etc.)\n3. Translate only the text content, NOT the Markdown syntax\n4. Keep the exact line structure and formatting\n5. Use natural, fluent Chinese expressions while maintaining accuracy\n6. Keep professional terminology precise\n7. Do not add explanations or introductory text - only provide the translated Markdown\n\nMarkdown content to translate:\n---\n${$json.text || 'No text content found'}`\n    }]\n  }]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, 560], "id": "md-gemini-translation", "name": "First Translation (Gemini) - MD"}, {"parameters": {"jsCode": "// 處理 MD Gemini API 回應\nconst geminiResponse = $json;\n\nlet sourceData = { originalFileName: 'Unknown', fileType: 'md', originalData: null };\nif ($('Extract MD Text').length > 0) {\n  sourceData = $('Extract MD Text').first().json;\n}\n\nlet translatedText = 'Translation failed';\nif (geminiResponse.candidates && geminiResponse.candidates[0] && geminiResponse.candidates[0].content && geminiResponse.candidates[0].content.parts && geminiResponse.candidates[0].content.parts[0]) {\n  translatedText = geminiResponse.candidates[0].content.parts[0].text;\n}\n\nconsole.log('🔍 Process MD Gemini Response:');\nconsole.log('  - fileType:', sourceData.fileType);\nconsole.log('  - fileName:', sourceData.originalFileName);\nconsole.log('  - 翻譯長度:', translatedText ? translatedText.length : 0);\n\nreturn {\n  initialTranslation: translatedText,\n  originalFileName: sourceData.originalFileName,\n  fileType: sourceData.fileType,\n  originalData: sourceData.originalData,\n  originalStructure: sourceData.originalStructure\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [200, 560], "id": "md-process-gemini", "name": "Process MD Gemini Response"}, {"parameters": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "************************************************************************************************************"}, {"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  \"model\": \"claude-3-5-sonnet-20240620\",\n  \"max_tokens\": 4096,\n  \"system\": \"你是一位資深的翻譯審校專家，專門處理 Markdown 文檔。你收到的內容是一段初步的中文翻譯。你的任務是進行以下改進：\\n1. **語意準確性**：確保翻譯完全傳達原文含義。\\n2. **語言自然性**：改善中文表達的流暢度和自然性，使其符合台灣讀者習慣。\\n3. **格式保持**：嚴格保持所有 Markdown 格式標記（標題、連結、代碼塊、列表等）。\\n\\n**最重要的規則是：** 必須保持原始的 Markdown 結構和換行符。只翻譯文本內容，不要修改任何 Markdown 語法。輸出的總行數必須與輸入的總行數完全相同。絕不添加任何額外的解釋或開場白，只需提供最終的、逐行對應的翻譯結果。\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": `初始中文翻譯：\\n---\\n${$json.initialTranslation}\\n---\\n\\n請提供改進後的翻譯版本。`\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, 560], "id": "md-claude-translation", "name": "Reflective Translation (<PERSON>) - MD"}, {"parameters": {"jsCode": "// 解析 MD Claude API 回應\nconst claudeResponse = $json;\n\nlet upstreamData = { \n  initialTranslation: 'No translation', \n  originalFileName: 'Unknown', \n  fileType: 'md', \n  originalData: null, \n  originalStructure: null \n};\n\nif ($('Process MD Gemini Response').length > 0) {\n  upstreamData = $('Process MD Gemini Response').first().json;\n}\n\nlet finalTranslation = upstreamData.initialTranslation;\nif (claudeResponse.content && claudeResponse.content[0] && claudeResponse.content[0].text) {\n  finalTranslation = claudeResponse.content[0].text;\n}\n\nconsole.log('🏆 Process MD Claude Response:');\nconsole.log('  - fileType:', upstreamData.fileType);\nconsole.log('  - fileName:', upstreamData.originalFileName);\nconsole.log('  - 最終翻譯長度:', finalTranslation ? finalTranslation.length : 0);\n\nreturn {\n  finalTranslation: finalTranslation.trim(),\n  originalFileName: upstreamData.originalFileName,\n  fileType: upstreamData.fileType,\n  originalData: upstreamData.originalData,\n  originalStructure: upstreamData.originalStructure\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [600, 560], "id": "md-process-claude", "name": "Process MD Claude Response"}, {"parameters": {"jsCode": "// 準備 MD 內容 - 準備檔名API調用\nconst inputData = $json;\nconst fileId = $('Google Drive Trigger').first().json.id;\n\nconsole.log('🔍 MD Filename Resolver:');\nconsole.log('  - 檔案ID:', fileId);\nconsole.log('  - 輸入數據 fileType:', inputData.fileType);\n\nif (!fileId) {\n  throw new Error('無法獲取檔案ID進行檔名解析');\n}\n\n// 返回數據並準備API調用\nreturn {\n  ...inputData,\n  fileIdForAPI: fileId,\n  apiUrl: `https://www.googleapis.com/drive/v3/files/${fileId}?fields=name`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [940, 560], "id": "md-filename-resolver", "name": "MD Filename Resolver"}, {"parameters": {"url": "={{ $json.apiUrl }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1080, 560], "id": "md-get-filename-api", "name": "Get MD Filename API", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 處理 MD 檔名API響應並準備內容\nconst apiResponse = $json;\nconst mdData = $('MD Filename Resolver').first().json;\n\nconsole.log('🎯 Process MD Filename API Response:');\nconsole.log('  - API Response:', JSON.stringify(apiResponse, null, 2));\n\nlet finalFileName = 'MDAPIFailed';\n\nif (apiResponse.name) {\n  finalFileName = apiResponse.name.replace(/\\.(md|markdown)$/i, '');\n  console.log('✅ 從API獲取MD檔名:', finalFileName);\n} else {\n  finalFileName = 'MD_Document_' + new Date().toISOString().substring(0, 10);\n  console.log('❌ API失敗，使用備案檔名:', finalFileName);\n}\n\nconst finalTitle = finalFileName + '_翻譯版';\n\nconsole.log('🏆 最終MD檔案標題:', finalTitle);\n\nreturn {\n  title: finalTitle,\n  content: mdData.finalTranslation,\n  finalFileName: finalFileName\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1220, 560], "id": "md-process-filename-response", "name": "Process MD Filename Response"}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/drive/v3/files", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  name: $json.title || 'Translated MD Document',\n  parents: ['1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg'],\n  mimeType: 'application/vnd.google-apps.document'\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1360, 560], "id": "md-create-google-doc", "name": "Create Google Doc for MD", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Get document info and content for MD\nconst docInfo = $json;\nconst documentId = docInfo.id;\n\nif (!documentId) {\n  throw new Error('Could not find document ID');\n}\n\nconst prepNodeItems = $('Process MD Filename Response').all();\n\nif (!prepNodeItems || prepNodeItems.length === 0) {\n    throw new Error('Could not find output from the \"Process MD Filename Response\" node.');\n}\n\nconst translationContent = prepNodeItems[0].json.content;\n\nif (!translationContent) {\n  throw new Error('Could not find \"content\" in the output of \"Process MD Filename Response\" node.');\n}\n\nreturn {\n  documentId: documentId,\n  content: translationContent,\n  documentUrl: `https://docs.google.com/document/d/${documentId}/edit`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1500, 560], "id": "md-prepare-final-payload", "name": "Prepare MD Final Payload"}, {"parameters": {"method": "POST", "url": "={{ `https://docs.googleapis.com/v1/documents/${$json.documentId}:batchUpdate` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDocsOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  requests: [\n    {\n      insertText: {\n        location: {\n          index: 1\n        },\n        text: $json.content || '無法獲取翻譯內容'\n      }\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1640, 560], "id": "md-add-content-to-doc", "name": "Add Content to MD Google Doc", "credentials": {"googleDocsOAuth2Api": {"id": "GtMyeGieoILXwpLs", "name": "Google Docs account"}}}, {"parameters": {"url": "={{ `https://drive.google.com/uc?export=download&id=${$json.id}` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-560, 720], "id": "word-download-node", "name": "Download Word File", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Word 檔案內容提取\nconst item = $input.item;\n\nconsole.log('📄 Extract Word Text:');\nconsole.log('  - Input item keys:', Object.keys(item));\n\nlet fileName = 'Unknown Word Document';\nlet fileId = 'unknown-id';\n\n// 從觸發器獲取文件信息\nif ($('Google Drive Trigger').length > 0) {\n  const triggerData = $('Google Drive Trigger').first().json;\n  fileName = triggerData.name || 'Unknown Word Document';\n  fileId = triggerData.id || 'unknown-id';\n  console.log('  - 從觸發器獲取文件名:', fileName);\n} else if ($('File Type Filter').length > 0) {\n  const filterData = $('File Type Filter').first().json;\n  fileName = filterData.name || 'Unknown Word Document';\n  fileId = filterData.id || 'unknown-id';\n  console.log('  - 從過濾器獲取文件名:', fileName);\n}\n\nlet extractedText = '';\n\n// 嘗試使用 mammoth.js 解析 Word 檔案\ntry {\n  const mammoth = require('mammoth');\n  \n  if (item.binary && item.binary.data) {\n    console.log('🔍 使用 mammoth.js 解析 Word 檔案');\n    \n    // 將 base64 數據轉換為 Buffer\n    const buffer = Buffer.from(item.binary.data, 'base64');\n    \n    // 使用 mammoth 提取文本\n    const result = await mammoth.extractRawText({buffer: buffer});\n    extractedText = result.value;\n    \n    console.log('✅ mammoth.js 解析成功');\n  } else {\n    throw new Error('No binary data found');\n  }\n} catch (error) {\n  console.log('⚠️ mammoth.js 不可用或解析失敗:', error.message);\n  console.log('🔄 嘗試備用方法...');\n  \n  // 備用方法：嘗試其他數據源\n  if (typeof item.json === 'string') {\n    extractedText = item.json;\n  } else if (item.json && typeof item.json.data === 'string') {\n    extractedText = item.json.data;\n  } else {\n    // 如果都失敗，提供安裝指引\n    extractedText = `Word 檔案解析需要安裝 mammoth.js 庫。\\n\\n安裝方法：\\n1. 在 N8N 環境中執行: npm install mammoth\\n2. 重啟 N8N 服務\\n\\n檔案名: ${fileName}\\n檔案ID: ${fileId}`;\n    console.log('❌ 無法解析 Word 檔案，需要安裝 mammoth.js');\n  }\n}\n\nif (!extractedText || extractedText.trim().length === 0) {\n  console.log('❌ 提取的文本為空');\n  throw new Error(`Extracted text from Word file is empty.`);\n}\n\nconst cleanedText = extractedText\n  .replace(/\\r\\n/g, '\\n')\n  .replace(/\\r/g, '\\n')\n  .trim();\n\nconst result = {\n  text: cleanedText,\n  originalFileId: fileId,\n  originalFileName: fileName,\n  fileType: 'word' // 明確設置為 word\n};\n\nconsole.log('✅ Word 文本提取完成:', {\n  fileType: result.fileType,\n  fileName: result.originalFileName,\n  textLength: result.text.length\n});\n\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-420, 720], "id": "word-extract-node", "name": "Extract Word Text"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=AIzaSyD5mbvRs_vIuAE9twS2wU-IpK_RMMHV9oo", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  contents: [{\n    parts: [{\n      text: `You are a professional translation engine specializing in English to Traditional Chinese (Taiwan) translation for Word documents. Your task:\n\n1. Translate the following text into Traditional Chinese suitable for Taiwan readers\n2. Preserve the document structure and formatting as much as possible\n3. Use natural, fluent Chinese expressions while maintaining accuracy\n4. Keep professional terminology precise\n5. Maintain paragraph breaks and structure\n6. Do not add explanations or introductory text - only provide the translation\n\nWord document content to translate:\n---\n${$json.text || 'No text content found'}`\n    }]\n  }]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, 720], "id": "word-gemini-translation", "name": "First Translation (Gemini) - Word"}, {"parameters": {"jsCode": "// 處理 Word Gemini API 回應\nconst geminiResponse = $json;\n\nlet sourceData = { originalFileName: 'Unknown', fileType: 'word', originalData: null };\nif ($('Extract Word Text').length > 0) {\n  sourceData = $('Extract Word Text').first().json;\n}\n\nlet translatedText = 'Translation failed';\nif (geminiResponse.candidates && geminiResponse.candidates[0] && geminiResponse.candidates[0].content && geminiResponse.candidates[0].content.parts && geminiResponse.candidates[0].content.parts[0]) {\n  translatedText = geminiResponse.candidates[0].content.parts[0].text;\n}\n\nconsole.log('🔍 Process Word Gemini Response:');\nconsole.log('  - fileType:', sourceData.fileType);\nconsole.log('  - fileName:', sourceData.originalFileName);\nconsole.log('  - 翻譯長度:', translatedText ? translatedText.length : 0);\n\nreturn {\n  initialTranslation: translatedText,\n  originalFileName: sourceData.originalFileName,\n  fileType: sourceData.fileType,\n  originalData: sourceData.originalData,\n  originalStructure: sourceData.originalStructure\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [200, 720], "id": "word-process-gemini", "name": "Process Word Gemini Response"}, {"parameters": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "************************************************************************************************************"}, {"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  \"model\": \"claude-3-5-sonnet-20240620\",\n  \"max_tokens\": 4096,\n  \"system\": \"你是一位資深的翻譯審校專家，專門處理 Word 文檔。你收到的內容是一段初步的中文翻譯。你的任務是進行以下改進：\\n1. **語意準確性**：確保翻譯完全傳達原文含義。\\n2. **語言自然性**：改善中文表達的流暢度和自然性，使其符合台灣讀者習慣。\\n3. **格式保持**：盡量保持文檔的段落結構和格式。\\n\\n**最重要的規則是：** 必須保持原始的段落結構和換行符。輸出的總行數必須與輸入的總行數完全相同。絕不添加任何額外的解釋或開場白，只需提供最終的、逐行對應的翻譯結果。\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": `初始中文翻譯：\\n---\\n${$json.initialTranslation}\\n---\\n\\n請提供改進後的翻譯版本。`\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, 720], "id": "word-claude-translation", "name": "Reflective Translation (<PERSON>) - Word"}, {"parameters": {"jsCode": "// 解析 Word Claude API 回應\nconst claudeResponse = $json;\n\nlet upstreamData = { \n  initialTranslation: 'No translation', \n  originalFileName: 'Unknown', \n  fileType: 'word', \n  originalData: null, \n  originalStructure: null \n};\n\nif ($('Process Word Gemini Response').length > 0) {\n  upstreamData = $('Process Word Gemini Response').first().json;\n}\n\nlet finalTranslation = upstreamData.initialTranslation;\nif (claudeResponse.content && claudeResponse.content[0] && claudeResponse.content[0].text) {\n  finalTranslation = claudeResponse.content[0].text;\n}\n\nconsole.log('🏆 Process Word Claude Response:');\nconsole.log('  - fileType:', upstreamData.fileType);\nconsole.log('  - fileName:', upstreamData.originalFileName);\nconsole.log('  - 最終翻譯長度:', finalTranslation ? finalTranslation.length : 0);\n\nreturn {\n  finalTranslation: finalTranslation.trim(),\n  originalFileName: upstreamData.originalFileName,\n  fileType: upstreamData.fileType,\n  originalData: upstreamData.originalData,\n  originalStructure: upstreamData.originalStructure\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [600, 720], "id": "word-process-claude", "name": "Process Word Claude Response"}, {"parameters": {"jsCode": "// 準備 Word 內容 - 準備檔名API調用\nconst inputData = $json;\nconst fileId = $('Google Drive Trigger').first().json.id;\n\nconsole.log('🔍 Word Filename Resolver:');\nconsole.log('  - 檔案ID:', fileId);\nconsole.log('  - 輸入數據 fileType:', inputData.fileType);\n\nif (!fileId) {\n  throw new Error('無法獲取檔案ID進行檔名解析');\n}\n\n// 返回數據並準備API調用\nreturn {\n  ...inputData,\n  fileIdForAPI: fileId,\n  apiUrl: `https://www.googleapis.com/drive/v3/files/${fileId}?fields=name`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [940, 720], "id": "word-filename-resolver", "name": "Word Filename Resolver"}, {"parameters": {"url": "={{ $json.apiUrl }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1080, 720], "id": "word-get-filename-api", "name": "Get Word Filename API", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 處理 Word 檔名API響應並準備內容\nconst apiResponse = $json;\nconst wordData = $('Word Filename Resolver').first().json;\n\nconsole.log('🎯 Process Word Filename API Response:');\nconsole.log('  - API Response:', JSON.stringify(apiResponse, null, 2));\n\nlet finalFileName = 'WordAPIFailed';\n\nif (apiResponse.name) {\n  finalFileName = apiResponse.name.replace(/\\.(docx?|doc)$/i, '');\n  console.log('✅ 從API獲取Word檔名:', finalFileName);\n} else {\n  finalFileName = 'Word_Document_' + new Date().toISOString().substring(0, 10);\n  console.log('❌ API失敗，使用備案檔名:', finalFileName);\n}\n\nconst finalTitle = finalFileName + '_翻譯版';\n\nconsole.log('🏆 最終Word檔案標題:', finalTitle);\n\nreturn {\n  title: finalTitle,\n  content: wordData.finalTranslation,\n  finalFileName: finalFileName\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1220, 720], "id": "word-process-filename-response", "name": "Process Word Filename Response"}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/drive/v3/files", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  name: $json.title || 'Translated Word Document',\n  parents: ['1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg'],\n  mimeType: 'application/vnd.google-apps.document'\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1360, 720], "id": "word-create-google-doc", "name": "Create Google Doc for Word", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Get document info and content for Word\nconst docInfo = $json;\nconst documentId = docInfo.id;\n\nif (!documentId) {\n  throw new Error('Could not find document ID');\n}\n\nconst prepNodeItems = $('Process Word Filename Response').all();\n\nif (!prepNodeItems || prepNodeItems.length === 0) {\n    throw new Error('Could not find output from the \"Process Word Filename Response\" node.');\n}\n\nconst translationContent = prepNodeItems[0].json.content;\n\nif (!translationContent) {\n  throw new Error('Could not find \"content\" in the output of \"Process Word Filename Response\" node.');\n}\n\nreturn {\n  documentId: documentId,\n  content: translationContent,\n  documentUrl: `https://docs.google.com/document/d/${documentId}/edit`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1500, 720], "id": "word-prepare-final-payload", "name": "Prepare Word Final Payload"}, {"parameters": {"method": "POST", "url": "={{ `https://docs.googleapis.com/v1/documents/${$json.documentId}:batchUpdate` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDocsOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  requests: [\n    {\n      insertText: {\n        location: {\n          index: 1\n        },\n        text: $json.content || '無法獲取翻譯內容'\n      }\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1640, 720], "id": "word-add-content-to-doc", "name": "Add Content to Word Google Doc", "credentials": {"googleDocsOAuth2Api": {"id": "GtMyeGieoILXwpLs", "name": "Google Docs account"}}}, {"parameters": {"url": "={{ `https://drive.google.com/uc?export=download&id=${$json.id}` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-560, 880], "id": "excel-download-node", "name": "Download Excel File", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Excel 檔案內容提取\nconst item = $input.item;\n\nconsole.log('📊 Extract Excel Text:');\nconsole.log('  - Input item keys:', Object.keys(item));\n\nlet fileName = 'Unknown Excel Document';\nlet fileId = 'unknown-id';\n\n// 從觸發器獲取文件信息\nif ($('Google Drive Trigger').length > 0) {\n  const triggerData = $('Google Drive Trigger').first().json;\n  fileName = triggerData.name || 'Unknown Excel Document';\n  fileId = triggerData.id || 'unknown-id';\n  console.log('  - 從觸發器獲取文件名:', fileName);\n} else if ($('File Type Filter').length > 0) {\n  const filterData = $('File Type Filter').first().json;\n  fileName = filterData.name || 'Unknown Excel Document';\n  fileId = filterData.id || 'unknown-id';\n  console.log('  - 從過濾器獲取文件名:', fileName);\n}\n\nlet extractedText = '';\nlet originalData = [];\nlet originalStructure = [];\n\n// 嘗試使用 xlsx 解析 Excel 檔案\ntry {\n  const XLSX = require('xlsx');\n  \n  if (item.binary && item.binary.data) {\n    console.log('🔍 使用 xlsx 解析 Excel 檔案');\n    \n    // 將 base64 數據轉換為 Buffer\n    const buffer = Buffer.from(item.binary.data, 'base64');\n    \n    // 使用 xlsx 讀取工作簿\n    const workbook = XLSX.read(buffer, {type: 'buffer'});\n    \n    // 獲取第一個工作表\n    const sheetName = workbook.SheetNames[0];\n    const worksheet = workbook.Sheets[sheetName];\n    \n    // 轉換為 JSON 格式\n    const jsonData = XLSX.utils.sheet_to_json(worksheet, {header: 1});\n    \n    // 處理數據\n    let combinedText = '';\n    jsonData.forEach((row, rowIndex) => {\n      originalData.push({\n        rowIndex: rowIndex,\n        values: row || []\n      });\n      \n      if (row && Array.isArray(row)) {\n        row.forEach(cell => {\n          const value = cell ? String(cell).trim() : '';\n          if (value) {\n            combinedText += value + '\\n';\n          }\n        });\n      }\n    });\n    \n    extractedText = combinedText;\n    originalStructure = jsonData;\n    \n    console.log('✅ xlsx 解析成功，行數:', jsonData.length);\n  } else {\n    throw new Error('No binary data found');\n  }\n} catch (error) {\n  console.log('⚠️ xlsx 不可用或解析失敗:', error.message);\n  console.log('🔄 嘗試備用方法...');\n  \n  // 備用方法：嘗試其他數據源\n  if (typeof item.json === 'string') {\n    extractedText = item.json;\n  } else if (item.json && typeof item.json.data === 'string') {\n    extractedText = item.json.data;\n  } else {\n    // 如果都失敗，提供安裝指引\n    extractedText = `Excel 檔案解析需要安裝 xlsx 庫。\\n\\n安裝方法：\\n1. 在 N8N 環境中執行: npm install xlsx\\n2. 重啟 N8N 服務\\n\\n檔案名: ${fileName}\\n檔案ID: ${fileId}`;\n    console.log('❌ 無法解析 Excel 檔案，需要安裝 xlsx');\n    \n    // 創建一個模擬的數據結構\n    originalData = [\n      {rowIndex: 0, values: ['需要', '安裝', 'xlsx', '庫']},\n      {rowIndex: 1, values: ['請', '參考', '安裝', '指引']}\n    ];\n    originalStructure = originalData.map(row => row.values);\n  }\n}\n\nif (!extractedText || extractedText.trim().length === 0) {\n  console.log('❌ 提取的文本為空');\n  throw new Error(`Extracted text from Excel file is empty.`);\n}\n\nconst cleanedText = extractedText\n  .replace(/\\r\\n/g, '\\n')\n  .replace(/\\r/g, '\\n')\n  .trim();\n\nconst result = {\n  text: cleanedText,\n  originalFileId: fileId,\n  originalFileName: fileName,\n  fileType: 'excel', // 明確設置為 excel\n  originalData: originalData,\n  originalStructure: originalStructure\n};\n\nconsole.log('✅ Excel 文本提取完成:', {\n  fileType: result.fileType,\n  fileName: result.originalFileName,\n  textLength: result.text.length,\n  rowCount: originalData.length\n});\n\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-420, 880], "id": "excel-extract-node", "name": "Extract Excel Text"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=AIzaSyD5mbvRs_vIuAE9twS2wU-IpK_RMMHV9oo", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  contents: [{\n    parts: [{\n      text: `You are a professional translation engine specializing in English to Traditional Chinese (Taiwan) translation for Excel spreadsheet content. Your task:\n\n1. Translate the following spreadsheet content into Traditional Chinese suitable for Taiwan readers\n2. Preserve the EXACT line structure - if input has N lines, output must have N lines\n3. Use natural, fluent Chinese expressions while maintaining accuracy\n4. Keep professional terminology precise\n5. Maintain the tabular structure and data relationships\n6. Do not add explanations or introductory text - only provide the translation\n\nExcel content to translate:\n---\n${$json.text || 'No text content found'}`\n    }]\n  }]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, 880], "id": "excel-gemini-translation", "name": "First Translation (Gemini) - Excel"}, {"parameters": {"jsCode": "// 處理 Excel Gemini API 回應\nconst geminiResponse = $json;\n\nlet sourceData = { originalFileName: 'Unknown', fileType: 'excel', originalData: null, originalStructure: null };\nif ($('Extract Excel Text').length > 0) {\n  sourceData = $('Extract Excel Text').first().json;\n}\n\nlet translatedText = 'Translation failed';\nif (geminiResponse.candidates && geminiResponse.candidates[0] && geminiResponse.candidates[0].content && geminiResponse.candidates[0].content.parts && geminiResponse.candidates[0].content.parts[0]) {\n  translatedText = geminiResponse.candidates[0].content.parts[0].text;\n}\n\nconsole.log('🔍 Process Excel Gemini Response:');\nconsole.log('  - fileType:', sourceData.fileType);\nconsole.log('  - fileName:', sourceData.originalFileName);\nconsole.log('  - 翻譯長度:', translatedText ? translatedText.length : 0);\n\nreturn {\n  initialTranslation: translatedText,\n  originalFileName: sourceData.originalFileName,\n  fileType: sourceData.fileType,\n  originalData: sourceData.originalData,\n  originalStructure: sourceData.originalStructure\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [200, 880], "id": "excel-process-gemini", "name": "Process Excel Gemini Response"}], "pinData": {}, "connections": {"Google Drive Trigger": {"main": [[{"node": "File Type Filter", "type": "main", "index": 0}]]}, "File Type Filter": {"main": [[{"node": "Export Google Docs as Text", "type": "main", "index": 0}], [{"node": "Read Sheet Data", "type": "main", "index": 0}], [{"node": "Download TXT File", "type": "main", "index": 0}], [{"node": "Download MD File", "type": "main", "index": 0}], [{"node": "Download Word File", "type": "main", "index": 0}], [{"node": "Download Excel File", "type": "main", "index": 0}]]}, "Export Google Docs as Text": {"main": [[{"node": "Extract Docs Text", "type": "main", "index": 0}]]}, "Extract Docs Text": {"main": [[{"node": "First Translation (Gemini) - Docs", "type": "main", "index": 0}]]}, "Read Sheet Data": {"main": [[{"node": "Format Sheet Data", "type": "main", "index": 0}]]}, "Docs Filename Resolver": {"main": [[{"node": "Get Docs Filename API", "type": "main", "index": 0}]]}, "Get Docs Filename API": {"main": [[{"node": "Process Docs Filename Response", "type": "main", "index": 0}]]}, "Process Docs Filename Response": {"main": [[{"node": "Create Google Doc via Drive API", "type": "main", "index": 0}]]}, "Create Google Doc via Drive API": {"main": [[{"node": "Prepare Final Payload", "type": "main", "index": 0}]]}, "Prepare Final Payload": {"main": [[{"node": "Add Content to Google Doc", "type": "main", "index": 0}]]}, "Rebuild Translated Sheet Data": {"main": [[{"node": "Filename Resolver", "type": "main", "index": 0}]]}, "Filename Resolver": {"main": [[{"node": "Get Filename API", "type": "main", "index": 0}]]}, "Get Filename API": {"main": [[{"node": "Process Filename Response", "type": "main", "index": 0}]]}, "Process Filename Response": {"main": [[{"node": "Create Translated Sheet", "type": "main", "index": 0}]]}, "Create Translated Sheet": {"main": [[{"node": "Prepare Sheet Write", "type": "main", "index": 0}]]}, "Prepare Sheet Write": {"main": [[{"node": "Move to Output Folder", "type": "main", "index": 0}]]}, "Move to Output Folder": {"main": [[{"node": "Rebuild Write Payload", "type": "main", "index": 0}]]}, "Rebuild Write Payload": {"main": [[{"node": "Write to Sheet", "type": "main", "index": 0}]]}, "Format Sheet Data": {"main": [[{"node": "First Translation (Gemini) - Sheets", "type": "main", "index": 0}]]}, "First Translation (Gemini) - Docs": {"main": [[{"node": "Process Docs Gemini Response", "type": "main", "index": 0}]]}, "First Translation (Gemini) - Sheets": {"main": [[{"node": "Process Sheets Gemini Response", "type": "main", "index": 0}]]}, "Process Docs Gemini Response": {"main": [[{"node": "Reflective Translation (Claude) - Docs", "type": "main", "index": 0}]]}, "Process Sheets Gemini Response": {"main": [[{"node": "Reflective Translation (<PERSON>) - Sheets", "type": "main", "index": 0}]]}, "Reflective Translation (Claude) - Docs": {"main": [[{"node": "Process Docs Claude Response", "type": "main", "index": 0}]]}, "Reflective Translation (Claude) - Sheets": {"main": [[{"node": "Process Sheets Claude Response", "type": "main", "index": 0}]]}, "Process Docs Claude Response": {"main": [[{"node": "Docs Filename Resolver", "type": "main", "index": 0}]]}, "Process Sheets Claude Response": {"main": [[{"node": "Rebuild Translated Sheet Data", "type": "main", "index": 0}]]}, "Download TXT File": {"main": [[{"node": "Extract TXT Text", "type": "main", "index": 0}]]}, "Extract TXT Text": {"main": [[{"node": "First Translation (Gemini) - TXT", "type": "main", "index": 0}]]}, "First Translation (Gemini) - TXT": {"main": [[{"node": "Process TXT Gemini Response", "type": "main", "index": 0}]]}, "Process TXT Gemini Response": {"main": [[{"node": "Reflective Translation (Claude) - TXT", "type": "main", "index": 0}]]}, "Reflective Translation (Claude) - TXT": {"main": [[{"node": "Process TXT Claude Response", "type": "main", "index": 0}]]}, "Process TXT Claude Response": {"main": [[{"node": "TXT Filename Resolver", "type": "main", "index": 0}]]}, "TXT Filename Resolver": {"main": [[{"node": "Get TXT Filename API", "type": "main", "index": 0}]]}, "Get TXT Filename API": {"main": [[{"node": "Process TXT Filename Response", "type": "main", "index": 0}]]}, "Process TXT Filename Response": {"main": [[{"node": "Create Google Doc for TXT", "type": "main", "index": 0}]]}, "Create Google Doc for TXT": {"main": [[{"node": "Prepare TXT Final Payload", "type": "main", "index": 0}]]}, "Prepare TXT Final Payload": {"main": [[{"node": "Add Content to TXT Google Doc", "type": "main", "index": 0}]]}, "Download MD File": {"main": [[{"node": "Extract MD Text", "type": "main", "index": 0}]]}, "Extract MD Text": {"main": [[{"node": "First Translation (Gemini) - MD", "type": "main", "index": 0}]]}, "First Translation (Gemini) - MD": {"main": [[{"node": "Process MD Gemini Response", "type": "main", "index": 0}]]}, "Process MD Gemini Response": {"main": [[{"node": "Reflective Translation (<PERSON>) - MD", "type": "main", "index": 0}]]}, "Reflective Translation (Claude) - MD": {"main": [[{"node": "Process MD Claude Response", "type": "main", "index": 0}]]}, "Process MD Claude Response": {"main": [[{"node": "MD Filename Resolver", "type": "main", "index": 0}]]}, "MD Filename Resolver": {"main": [[{"node": "Get MD Filename API", "type": "main", "index": 0}]]}, "Get MD Filename API": {"main": [[{"node": "Process MD Filename Response", "type": "main", "index": 0}]]}, "Process MD Filename Response": {"main": [[{"node": "Create Google Doc for MD", "type": "main", "index": 0}]]}, "Create Google Doc for MD": {"main": [[{"node": "Prepare MD Final Payload", "type": "main", "index": 0}]]}, "Prepare MD Final Payload": {"main": [[{"node": "Add Content to MD Google Doc", "type": "main", "index": 0}]]}, "Download Word File": {"main": [[{"node": "Extract Word Text", "type": "main", "index": 0}]]}, "Extract Word Text": {"main": [[{"node": "First Translation (Gemini) - Word", "type": "main", "index": 0}]]}, "First Translation (Gemini) - Word": {"main": [[{"node": "Process Word Gemini Response", "type": "main", "index": 0}]]}, "Process Word Gemini Response": {"main": [[{"node": "Reflective Translation (<PERSON>) - Word", "type": "main", "index": 0}]]}, "Reflective Translation (Claude) - Word": {"main": [[{"node": "Process Word Claude Response", "type": "main", "index": 0}]]}, "Process Word Claude Response": {"main": [[{"node": "Word Filename Resolver", "type": "main", "index": 0}]]}, "Word Filename Resolver": {"main": [[{"node": "Get Word Filename API", "type": "main", "index": 0}]]}, "Get Word Filename API": {"main": [[{"node": "Process Word Filename Response", "type": "main", "index": 0}]]}, "Process Word Filename Response": {"main": [[{"node": "Create Google Doc for Word", "type": "main", "index": 0}]]}, "Create Google Doc for Word": {"main": [[{"node": "Prepare Word Final Payload", "type": "main", "index": 0}]]}, "Prepare Word Final Payload": {"main": [[{"node": "Add Content to Word Google Doc", "type": "main", "index": 0}]]}, "Download Excel File": {"main": [[{"node": "Extract Excel Text", "type": "main", "index": 0}]]}, "Extract Excel Text": {"main": [[{"node": "First Translation (Gemini) - Excel", "type": "main", "index": 0}]]}, "First Translation (Gemini) - Excel": {"main": [[{"node": "Process Excel Gemini Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "317f56e5-4d74-4b90-a61f-36594a1d33a6", "meta": {"templateCredsSetupCompleted": true, "instanceId": "387b19747c54910162bb5357a1e4c84cc6ef858c20498d78b35149542a05c3de"}, "id": "CYNgk84nxORqIPO4", "tags": []}