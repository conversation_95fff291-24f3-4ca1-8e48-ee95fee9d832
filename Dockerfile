# N8N 翻譯工作流程自定義 Docker 映像
# 包含 mammoth.js 和 xlsx 庫以支援 Word 和 Excel 檔案處理

FROM n8nio/n8n:latest

# 設定維護者資訊
LABEL maintainer="N8N Translation Workflow"
LABEL description="N8N with mammoth.js and xlsx libraries for document translation workflow"
LABEL version="1.0.0"

# 切換到 root 用戶以安裝套件
USER root

# 更新套件列表並安裝必要的系統依賴
RUN apk update && apk add --no-cache \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# 安裝翻譯工作流程所需的 Node.js 庫
RUN npm install -g \
    mammoth@^1.6.0 \
    xlsx@^0.18.5 \
    && npm cache clean --force

# 創建測試腳本目錄
RUN mkdir -p /usr/local/lib/n8n-translation

# 複製測試腳本
COPY test-libraries.js /usr/local/lib/n8n-translation/
COPY n8n-translation-dependencies.json /usr/local/lib/n8n-translation/package.json

# 設定執行權限
RUN chmod +x /usr/local/lib/n8n-translation/test-libraries.js

# 切換回 node 用戶
USER node

# 設定環境變數
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV N8N_CUSTOM_EXTENSIONS="/usr/local/lib/n8n-translation"

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:5678/healthz || exit 1

# 創建啟動腳本
USER root
RUN echo '#!/bin/sh' > /usr/local/bin/start-n8n.sh && \
    echo 'echo "🔍 檢查依賴庫安裝狀態..."' >> /usr/local/bin/start-n8n.sh && \
    echo 'node /usr/local/lib/n8n-translation/test-libraries.js' >> /usr/local/bin/start-n8n.sh && \
    echo 'if [ $? -eq 0 ]; then' >> /usr/local/bin/start-n8n.sh && \
    echo '  echo "✅ 所有依賴庫已就緒，啟動 N8N..."' >> /usr/local/bin/start-n8n.sh && \
    echo 'else' >> /usr/local/bin/start-n8n.sh && \
    echo '  echo "⚠️  依賴庫檢查失敗，但仍嘗試啟動 N8N..."' >> /usr/local/bin/start-n8n.sh && \
    echo 'fi' >> /usr/local/bin/start-n8n.sh && \
    echo 'exec n8n start' >> /usr/local/bin/start-n8n.sh && \
    chmod +x /usr/local/bin/start-n8n.sh

USER node

# 暴露 N8N 預設端口
EXPOSE 5678

# 設定工作目錄
WORKDIR /home/<USER>

# 使用自定義啟動腳本
CMD ["/usr/local/bin/start-n8n.sh"]
