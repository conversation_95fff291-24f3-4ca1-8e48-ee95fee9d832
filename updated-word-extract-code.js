// Word 檔案內容提取 - 增強版診斷
const item = $input.item;

console.log('📄 Extract Word Text - Enhanced Diagnostics:');
console.log('  - Input item keys:', Object.keys(item));
console.log('  - Node.js version:', process.version);
console.log('  - Working directory:', process.cwd());

let fileName = 'Unknown Word Document';
let fileId = 'unknown-id';

// 從觸發器獲取文件信息
if ($('Google Drive Trigger').length > 0) {
  const triggerData = $('Google Drive Trigger').first().json;
  fileName = triggerData.name || 'Unknown Word Document';
  fileId = triggerData.id || 'unknown-id';
  console.log('  - 從觸發器獲取文件名:', fileName);
} else if ($('File Type Filter').length > 0) {
  const filterData = $('File Type Filter').first().json;
  fileName = filterData.name || 'Unknown Word Document';
  fileId = filterData.id || 'unknown-id';
  console.log('  - 從過濾器獲取文件名:', fileName);
}

let extractedText = '';
let mammothLoadError = null;

// 多種方式嘗試載入 mammoth.js
let mammoth = null;
const loadAttempts = [
  () => require('mammoth'),
  () => require('/usr/local/lib/node_modules/mammoth'),
  () => require('/usr/lib/node_modules/mammoth'),
  () => require(process.cwd() + '/node_modules/mammoth')
];

for (let i = 0; i < loadAttempts.length; i++) {
  try {
    console.log(`🔍 嘗試載入方式 ${i + 1}...`);
    mammoth = loadAttempts[i]();
    console.log('✅ mammoth.js 載入成功！');
    break;
  } catch (error) {
    console.log(`❌ 載入方式 ${i + 1} 失敗:`, error.message);
    mammothLoadError = error;
  }
}

if (mammoth) {
  try {
    if (item.binary && item.binary.data) {
      console.log('🔍 使用 mammoth.js 解析 Word 檔案');
      console.log('  - Binary data size:', item.binary.data.length);
      
      // 將 base64 數據轉換為 Buffer
      const buffer = Buffer.from(item.binary.data, 'base64');
      console.log('  - Buffer size:', buffer.length);
      
      // 使用 mammoth 提取文本
      const result = await mammoth.extractRawText({buffer: buffer});
      extractedText = result.value;
      
      console.log('✅ mammoth.js 解析成功');
      console.log('  - 提取文本長度:', extractedText.length);
      
      if (result.messages && result.messages.length > 0) {
        console.log('  - Mammoth 訊息:', result.messages);
      }
    } else {
      throw new Error('No binary data found in item');
    }
  } catch (parseError) {
    console.log('❌ mammoth.js 解析失敗:', parseError.message);
    extractedText = `Word 檔案解析失敗。\n\n錯誤詳情：\n${parseError.message}\n\n檔案名: ${fileName}\n檔案ID: ${fileId}\n\n請檢查檔案是否為有效的 Word 文檔。`;
  }
} else {
  console.log('❌ 無法載入 mammoth.js');
  console.log('最後一個錯誤:', mammothLoadError?.message);
  
  // 提供詳細的診斷信息
  const diagnosticInfo = {
    nodeVersion: process.version,
    platform: process.platform,
    workingDir: process.cwd(),
    modulePaths: require.main.paths.slice(0, 3), // 只顯示前3個路徑
    lastError: mammothLoadError?.message,
    fileName: fileName,
    fileId: fileId
  };
  
  extractedText = `Word 檔案解析需要 mammoth.js 庫，但載入失敗。\n\n診斷信息：\n${JSON.stringify(diagnosticInfo, null, 2)}\n\n解決方案：\n1. 檢查 mammoth.js 是否已安裝: npm list mammoth\n2. 全域安裝: npm install -g mammoth\n3. 本地安裝: npm install mammoth\n4. 重啟 N8N 服務\n\n如果問題持續，請在 N8N Code 節點中運行診斷腳本。`;
}

if (!extractedText || extractedText.trim().length === 0) {
  console.log('❌ 提取的文本為空');
  extractedText = `Word 檔案內容為空或無法讀取。\n\n檔案信息：\n- 檔案名: ${fileName}\n- 檔案ID: ${fileId}\n- 是否有二進制數據: ${!!(item.binary && item.binary.data)}`;
}

const cleanedText = extractedText
  .replace(/\r\n/g, '\n')
  .replace(/\r/g, '\n')
  .trim();

const result = {
  text: cleanedText,
  originalFileId: fileId,
  originalFileName: fileName,
  fileType: 'word',
  mammothAvailable: !!mammoth,
  diagnosticInfo: {
    nodeVersion: process.version,
    hasData: !!(item.binary && item.binary.data),
    textLength: cleanedText.length
  }
};

console.log('✅ Word 文本提取完成:', {
  fileType: result.fileType,
  fileName: result.originalFileName,
  textLength: result.text.length,
  mammothAvailable: result.mammothAvailable
});

return result;
