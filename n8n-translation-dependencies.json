{"name": "n8n-translation-workflow-dependencies", "version": "1.0.0", "description": "Dependencies for N8N translation workflow supporting multiple file formats", "main": "index.js", "scripts": {"test": "node test-libraries.js", "install-deps": "npm install mammoth xlsx", "check-deps": "node test-libraries.js"}, "dependencies": {"mammoth": "^1.6.0", "xlsx": "^0.18.5"}, "devDependencies": {}, "keywords": ["n8n", "translation", "workflow", "word", "excel", "document-processing"], "author": "N8N Translation Workflow", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-repo/n8n-translation-workflow"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"n8n": ">=0.200.0"}}