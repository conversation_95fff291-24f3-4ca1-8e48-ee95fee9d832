{"name": "Word Processing Fix - Standalone", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "https://drive.google.com/drive/u/2/folders/1LADP6VD4ojtFyjuTNiNwLS3jB25tXG8X", "mode": "url"}, "event": "fileUpdated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-840, 140], "id": "trigger-node", "name": "Google Drive Trigger", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "word-file-condition", "leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-700, 140], "id": "filter-node", "name": "Word File Filter"}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/drive/v3/files/copy", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  name: $json.name + '_temp_conversion',\n  mimeType: 'application/vnd.google-apps.document'\n}) }}", "sendQuery": true, "queryParameters": {"parameters": [{"name": "fileId", "value": "={{ $json.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-560, 140], "id": "convert-node", "name": "Convert Word to Google Docs", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"url": "={{ `https://docs.google.com/document/d/${$json.id}/export?format=txt` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-420, 140], "id": "export-node", "name": "Export as Text", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Word 檔案內容提取 - 通過 Google Docs 轉換\nconst item = $input.item;\n\nconsole.log('📄 Extract Word Text via Google Docs:');\nconsole.log('  - Input item keys:', Object.keys(item));\n\nlet fileName = 'Unknown Word Document';\nlet fileId = 'unknown-id';\n\n// 從觸發器獲取原始文件信息\nif ($('Google Drive Trigger').length > 0) {\n  const triggerData = $('Google Drive Trigger').first().json;\n  fileName = triggerData.name || 'Unknown Word Document';\n  fileId = triggerData.id || 'unknown-id';\n  console.log('  - 從觸發器獲取原始文件名:', fileName);\n}\n\nlet extractedText = '';\n\n// 從轉換後的 Google Docs 提取文本\nif (typeof item.json === 'string') {\n  extractedText = item.json;\n} else if (item.json && typeof item.json.data === 'string') {\n  extractedText = item.json.data;\n} else {\n  console.log('❌ 無法找到文本內容');\n  console.log('  - item.json type:', typeof item.json);\n  console.log('  - item structure:', JSON.stringify(item, null, 2));\n  throw new Error(`Could not find text content in the converted document response`);\n}\n\nif (!extractedText || extractedText.trim().length === 0) {\n  console.log('❌ 提取的文本為空');\n  throw new Error(`Extracted text from converted Word document is empty.`);\n}\n\nconst cleanedText = extractedText\n  .replace(/\\r\\n/g, '\\n')\n  .replace(/\\r/g, '\\n')\n  .trim();\n\nconst result = {\n  text: cleanedText,\n  originalFileId: fileId,\n  originalFileName: fileName,\n  fileType: 'word',\n  extractionMethod: 'google-docs-conversion'\n};\n\nconsole.log('✅ Word 文本提取完成 (via Google Docs):');\nconsole.log('  - fileType:', result.fileType);\nconsole.log('  - fileName:', result.originalFileName);\nconsole.log('  - textLength:', result.text.length);\nconsole.log('  - text preview:', result.text.substring(0, 200) + '...');\n\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-280, 140], "id": "extract-node", "name": "Extract Word Text"}, {"parameters": {"method": "DELETE", "url": "={{ `https://www.googleapis.com/drive/v3/files/${$('Convert Word to Google Docs').first().json.id}` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-140, 140], "id": "cleanup-node", "name": "Cleanup Temp File", "credentials": {"googleDriveOAuth2Api": {"id": "QZ9EJOfx5C7rh1Yx", "name": "Google Drive account"}}}], "connections": {"Google Drive Trigger": {"main": [[{"node": "Word File Filter", "type": "main", "index": 0}]]}, "Word File Filter": {"main": [[{"node": "Convert Word to Google Docs", "type": "main", "index": 0}]]}, "Convert Word to Google Docs": {"main": [[{"node": "Export as Text", "type": "main", "index": 0}]]}, "Export as Text": {"main": [[{"node": "Extract Word Text", "type": "main", "index": 0}]]}, "Extract Word Text": {"main": [[{"node": "Cleanup Temp File", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "word-fix-v1", "meta": {"templateCredsSetupCompleted": true}, "tags": []}