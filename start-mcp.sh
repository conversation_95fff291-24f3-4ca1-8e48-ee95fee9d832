#!/bin/bash
echo "🚀 啟動 n8n MCP 服務..."

# 載入環境變數
if [ -f ".env" ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# 檢查是否使用 Docker
if command -v docker &> /dev/null && [ -f "docker-compose.mcp.yml" ]; then
    echo "🐳 使用 Docker 啟動..."
    docker-compose -f docker-compose.mcp.yml up -d
    echo "✅ 服務已啟動"
    echo "n8n: http://localhost:5678"
    echo "MCP Server: http://localhost:3001"
else
    echo "💻 本地模式啟動..."
    echo "請確保環境變數已設定並手動啟動 n8n"
fi
