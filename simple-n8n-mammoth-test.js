// 簡單的 mammoth.js 測試 - N8N 兼容版本
console.log('🧪 開始 mammoth.js 測試...');

try {
  const mammoth = require('mammoth');
  console.log('✅ mammoth.js 載入成功');
  console.log('mammoth 對象類型:', typeof mammoth);
  
  if (mammoth.extractRawText) {
    console.log('✅ extractRawText 方法可用');
  } else {
    console.log('❌ extractRawText 方法不可用');
  }
  
  return {
    status: 'success',
    mammoth_available: true,
    has_extractRawText: !!mammoth.extractRawText
  };
  
} catch (error) {
  console.log('❌ mammoth.js 測試失敗');
  console.log('錯誤:', error.message);
  
  return {
    status: 'failed',
    mammoth_available: false,
    error: error.message
  };
}
