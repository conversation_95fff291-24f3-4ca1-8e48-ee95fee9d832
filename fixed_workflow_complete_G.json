{"name": "My workflow 4", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "https://drive.google.com/drive/u/2/folders/1LADP6VD4ojtFyjuTNiNwLS3jB25tXG8X", "mode": "url"}, "event": "fileUpdated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-1260, -240], "id": "e2ec4173-9e30-4f2f-afdd-685cf890a2a9", "name": "Google Drive Trigger", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.google-apps.document", "operator": {"type": "string", "operation": "equals"}, "id": "075a29bb-26e8-4d9d-8a3a-3718199ff9cc"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b5703c09-b09f-4fe5-a1c1-c82de4fe23c0", "leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.google-apps.spreadsheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-960, -240], "id": "a87757c5-47a1-49d4-a0e5-b2d5a5e4272e", "name": "File Type Filter"}, {"parameters": {"url": "={{ `https://docs.google.com/document/d/${$json.id}/export?format=txt` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-740, -300], "id": "a5ab84ec-01b0-4d33-b1d6-788144a24558", "name": "Export Google Docs as Text", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "options": {"googleFileConversion": {"conversion": {"sheetsToFormat": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}}}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-740, -180], "id": "83b2f07b-b596-429d-a89c-a4737b49a465", "name": "Download Google Sheets as Excel", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Google Docs 真實內容提取 - HTTP Export 方法\nconsole.log('=== GOOGLE DOCS REAL CONTENT EXTRACTION ===');\n\nlet extractedText = '';\nlet fileName = 'Unknown Document';\nlet fileId = 'unknown-id';\nlet debugInfo = {};\n\nconsole.log('HTTP Response type:', typeof $json);\nconsole.log('HTTP Response data:', $json);\n\n// 從 HTTP Export 響應中提取純文本內容\nif (typeof $json === 'string' && $json.trim().length > 0) {\n  // 直接文本響應\n  extractedText = $json.trim();\n  debugInfo.method = 'http_export_string';\n  console.log('✅ Got string response from Google Docs export');\n  \n} else if ($json && typeof $json === 'object') {\n  // 檢查可能的文本字段\n  const textFields = ['text', 'content', 'body', 'data'];\n  \n  for (const field of textFields) {\n    if ($json[field] && typeof $json[field] === 'string' && $json[field].trim().length > 0) {\n      extractedText = $json[field].trim();\n      debugInfo.method = `http_export_object_${field}`;\n      console.log(`✅ Found text content in field: ${field}`);\n      break;\n    }\n  }\n  \n  // 如果是對象但沒有找到文本，記錄結構\n  if (!extractedText) {\n    debugInfo.responseKeys = Object.keys($json);\n    console.log('❌ Object response but no text content found');\n    console.log('Available keys:', Object.keys($json));\n  }\n  \n} else {\n  console.log('❌ Unexpected response type or empty response');\n  debugInfo.method = 'unexpected_response';\n  debugInfo.responseType = typeof $json;\n}\n\n// 從上游節點獲取檔案信息\nconsole.log('🔍 Extracting filename from upstream nodes...');\n\nif ($('File Type Filter').length > 0) {\n  const filterData = $('File Type Filter').first().json;\n  console.log('File Type Filter data:', JSON.stringify(filterData, null, 2));\n  if (filterData && filterData.name) {\n    fileName = filterData.name;\n    fileId = filterData.id || fileId;\n    console.log('✅ Got filename from File Type Filter:', fileName);\n  }\n} else {\n  console.log('❌ File Type Filter node not available');\n}\n\nif (!fileName || fileName === 'Unknown Document') {\n  if ($('Google Drive Trigger').length > 0) {\n    const triggerData = $('Google Drive Trigger').first().json;\n    console.log('Google Drive Trigger data:', JSON.stringify(triggerData, null, 2));\n    if (triggerData && triggerData.name) {\n      fileName = triggerData.name;\n      fileId = triggerData.id || fileId;\n      console.log('✅ Got filename from Google Drive Trigger:', fileName);\n    }\n  } else {\n    console.log('❌ Google Drive Trigger node not available');\n  }\n}\n\nconsole.log('📝 Final filename determined:', fileName);\nconsole.log('🆔 Final file ID determined:', fileId);\n\n// 處理提取結果\nif (extractedText && extractedText.length > 0) {\n  // 清理和格式化文本\n  extractedText = extractedText\n    .replace(/\\r\\n/g, ' ')     // 替換 Windows 換行\n    .replace(/\\n/g, ' ')       // 替換 Unix 換行\n    .replace(/\\s+/g, ' ')      // 合併多個空格\n    .trim();\n  \n  // 確保內容不為空\n  if (extractedText.length === 0) {\n    console.log('❌ Content became empty after cleaning');\n    debugInfo.cleaningIssue = true;\n  } else {\n    console.log(`📄 Processing file: ${fileName} (${fileId})`);\n    console.log(`📝 Final extracted text: ${extractedText.length} characters`);\n    console.log('📖 Content preview:', extractedText.substring(0, 300) + '...');\n    \n    return {\n      text: extractedText,\n      originalFileId: fileId,\n      originalFileName: fileName,\n      fileType: 'docs',\n      extractionMethod: debugInfo.method,\n      contentLength: extractedText.length,\n      debugInfo: debugInfo\n    };\n  }\n}\n\n// 如果提取失敗，返回錯誤信息\nconsole.log('❌ Failed to extract any text content from Google Docs export');\nconsole.log('Debug info:', JSON.stringify(debugInfo, null, 2));\n\nreturn {\n  text: `ERROR: Failed to extract content from Google Docs '${fileName}' (${fileId}). Export may have failed or document is empty.`,\n  originalFileId: fileId,\n  originalFileName: fileName,\n  fileType: 'docs',\n  extractionMethod: 'failed',\n  contentLength: 0,\n  debugInfo: debugInfo\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-520, -300], "id": "799f4538-1e35-43d2-8e50-7ed776a79e8b", "name": "Extract Docs Text"}, {"parameters": {"operation": "read"}, "type": "n8n-nodes-base.spreadsheetFile", "typeVersion": 2, "position": [-520, -180], "id": "3442d3ff-09e9-4e7b-9ed4-3def0a7b16ad", "name": "Parse Sheets Content"}, {"parameters": {"jsCode": "// 處理 Google Sheets 內容，將所有文字內容合併\nconst sheetsData = $input.all();\nlet combinedText = '';\n\nconsole.log('=== EXTRACT SHEETS TEXT ===');\nconsole.log('Sheets data length:', sheetsData.length);\n\n// 遍歷所有工作表數據\nsheetsData.forEach((sheet, sheetIndex) => {\n  if (sheet.json && typeof sheet.json === 'object') {\n    // 遍歷每個欄位\n    Object.keys(sheet.json).forEach(key => {\n      const value = sheet.json[key];\n      if (typeof value === 'string' && value.trim()) {\n        combinedText += value + '\\n';\n      }\n    });\n  }\n});\n\n// 嘗試從 File Type Filter 或 Google Drive Trigger 獲取文件信息\nlet fileData = null;\nif ($('File Type Filter').length > 0) {\n  fileData = $('File Type Filter').first().json;\n  console.log('✅ Got file data from File Type Filter:', fileData?.name);\n} else if ($('Google Drive Trigger').length > 0) {\n  fileData = $('Google Drive Trigger').first().json;\n  console.log('✅ Got file data from Google Drive Trigger:', fileData?.name);\n} else {\n  console.log('❌ No file data available from upstream nodes');\n  fileData = {};\n}\n\nconst result = {\n  text: combinedText.trim(),\n  originalFileId: fileData?.id || '',\n  originalFileName: fileData?.name || 'Unknown Sheets',\n  fileType: 'sheets',\n  originalData: sheetsData\n};\n\nconsole.log('📊 Sheets extraction result:', {\n  textLength: result.text.length,\n  fileName: result.originalFileName,\n  fileId: result.originalFileId\n});\n\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-320, -180], "id": "ee619652-ead4-496c-825e-1acc595977f0", "name": "Extract Sheets Text"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [-120, -240], "id": "fec890ae-f8e5-44e9-99ad-be8144433966", "name": "Merge Extracted Content"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=AIzaSyD5mbvRs_vIuAE9twS2wU-IpK_RMMHV9oo", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  contents: [{\n    parts: [{\n      text: `請將以下英文內容翻譯成繁體中文，要求保持原文語意和語調，使用自然流暢的中文表達，保留專業術語的準確性，適合台灣讀者閱讀：\\n\\n${$json.text || 'No text content found'}`\n    }]\n  }]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [80, -240], "id": "5586a97e-e647-4532-b338-975dc355ec2b", "name": "First Translation (<PERSON>)"}, {"parameters": {"jsCode": "// 處理 Gemini API 回應\nconst geminiResponse = $json;\n\n// 嘗試從多個源獲取數據\nlet sourceData = {};\n\n// 首先嘗試從 Merge Extracted Content 獲取\nif ($('Merge Extracted Content').length > 0) {\n  sourceData = $('Merge Extracted Content').first().json;\n  console.log('✅ Got source data from Merge Extracted Content');\n} \n// 如果 Merge 沒有數據，嘗試直接從 Extract Docs Text 獲取\nelse if ($('Extract Docs Text').length > 0) {\n  sourceData = $('Extract Docs Text').first().json;\n  console.log('✅ Got source data from Extract Docs Text (fallback)');\n}\n// 最後嘗試從 Extract Sheets Text 獲取\nelse if ($('Extract Sheets Text').length > 0) {\n  sourceData = $('Extract Sheets Text').first().json;\n  console.log('✅ Got source data from Extract Sheets Text (fallback)');\n}\nelse {\n  console.log('❌ No source data available from any extraction node');\n}\n\nconsole.log('=== GEMINI API RESPONSE ===');\nconsole.log('Gemini response:', JSON.stringify(geminiResponse, null, 2));\nconsole.log('Source data:', JSON.stringify(sourceData, null, 2));\n\n// 解析翻譯結果\nlet translatedText = 'Translation failed';\nif (geminiResponse.candidates && geminiResponse.candidates[0] && geminiResponse.candidates[0].content && geminiResponse.candidates[0].content.parts && geminiResponse.candidates[0].content.parts[0]) {\n  translatedText = geminiResponse.candidates[0].content.parts[0].text;\n}\n\nconsole.log('Extracted translation:', translatedText);\n\n// 檢查並記錄每個字段\nconsole.log('🔍 Analyzing source data fields:');\nconsole.log('- originalText:', sourceData.text ? `Present (${sourceData.text.length} chars)` : 'Missing');\nconsole.log('- originalFileName:', sourceData.originalFileName || 'Missing');\nconsole.log('- originalFileId:', sourceData.originalFileId || 'Missing');\nconsole.log('- fileType:', sourceData.fileType || 'Missing');\nconsole.log('- fileType actual value:', JSON.stringify(sourceData.fileType));\nconsole.log('- originalData:', sourceData.originalData ? 'Present' : 'Missing');\nconsole.log('- Full sourceData keys:', Object.keys(sourceData));\n\n// 傳遞完整數據到 Claude 進行改進\nconst result = {\n  initialTranslation: translatedText,\n  originalText: sourceData.text || 'No original text',\n  originalFileName: sourceData.originalFileName || 'Unknown',\n  originalFileId: sourceData.originalFileId || '',\n  fileType: sourceData.fileType || 'unknown',\n  originalData: sourceData.originalData\n};\n\nconsole.log('📤 Data being passed to Claude:');\nconsole.log('- fileType:', result.fileType);\nconsole.log('- originalFileName:', result.originalFileName);\n\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [280, -240], "id": "8e24e76b-cdb1-44a7-a146-8c6746c8dafc", "name": "Process Gemini Response"}, {"parameters": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "************************************************************************************************************"}, {"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  model: 'claude-3-5-sonnet-20240620',\n  max_tokens: 4096,\n  system: '你是一位資深的翻譯審校專家，擅長反思式翻譯改進。請仔細審查提供的中文翻譯，並進行以下改進：\\n\\n1. **語意準確性**：確保翻譯完全傳達原文含義\\n2. **語言自然性**：改善中文表達的流暢度和自然性\\n3. **用詞精準性**：選擇更恰當的詞彙和表達方式\\n4. **語境適配性**：確保翻譯符合台灣讀者的語言習慣\\n5. **專業一致性**：保持專業術語的準確性\\n\\n請提供改進後的翻譯版本，只需要提供最終的翻譯結果，不需要額外的解釋。',\n  messages: [\n    {\n      role: 'user',\n      content: `初始中文翻譯：${$json.initialTranslation}\\n\\n請提供改進後的翻譯版本。`\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [480, -240], "id": "75fe9de1-7b99-449b-81ec-a969f0e7bd6a", "name": "Reflective Translation (<PERSON>)"}, {"parameters": {"jsCode": "// 解析 Claude API 回應\nconst claudeResponse = $json;\nconst upstreamData = $('Process Gemini Response').length > 0 ? \n  $('Process Gemini Response').first().json : {};\n\nconsole.log('=== PROCESS CLAUDE RESPONSE ===');\nconsole.log('Claude response:', JSON.stringify(claudeResponse, null, 2));\nconsole.log('Upstream data from Gemini:', JSON.stringify(upstreamData, null, 2));\n\nlet finalTranslation = upstreamData.initialTranslation || 'No translation available';\n\ntry {\n  if (claudeResponse.content && claudeResponse.content[0] && claudeResponse.content[0].text) {\n    finalTranslation = claudeResponse.content[0].text;\n    console.log('✅ Using Claude improved translation');\n  } else {\n    console.log('⚠️ Using Gemini initial translation as fallback');\n  }\n} catch (e) {\n  console.error('解析 Claude 回應時出錯:', e.message);\n}\n\n// 確定文件類型：如果我們到達這裡，說明是通過 Gemini 翻譯流程的，\n// 而 Gemini 只處理合併後的文本，我們需要推斷文件類型\nlet determinedFileType = upstreamData.fileType || 'unknown';\n\n// 如果 fileType 是 unknown，嘗試從其他信息推斷\nif (determinedFileType === 'unknown') {\n  // 從 originalData 的結構推斷（更安全的方法）\n  if (upstreamData.originalData && Array.isArray(upstreamData.originalData)) {\n    determinedFileType = 'sheets';\n    console.log('🔧 Inferred fileType as \"sheets\" from originalData array structure');\n  }\n  else {\n    // 簡單默認為 docs，避免節點引用問題\n    determinedFileType = 'docs';\n    console.log('🔧 Defaulting fileType to \"docs\" as fallback');\n  }\n}\n\n// 傳遞最終數據到輸出交換機\nconst result = {\n  finalTranslation: finalTranslation.trim(),\n  originalFileId: upstreamData.originalFileId || '',\n  originalFileName: upstreamData.originalFileName || 'Unknown',\n  fileType: determinedFileType,\n  originalData: upstreamData.originalData\n};\n\nconsole.log('🔄 Data being passed to Output Type Switch:');\nconsole.log('- finalTranslation length:', result.finalTranslation.length);\nconsole.log('- originalFileName:', result.originalFileName);\nconsole.log('- fileType:', result.fileType);\nconsole.log('- fileType actual value:', JSON.stringify(result.fileType));\nconsole.log('- originalFileId:', result.originalFileId);\nconsole.log('🔍 Upstream data fileType:', JSON.stringify(upstreamData.fileType));\nconsole.log('🔍 All upstream data keys:', Object.keys(upstreamData));\n\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, -240], "id": "b53fee3e-0348-4068-afab-456cf1e22ff5", "name": "Process Claude Response"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.fileType }}", "rightValue": "docs", "operator": {"type": "string", "operation": "equals"}, "id": "docs-condition"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "sheets-condition", "leftValue": "={{ $json.fileType }}", "rightValue": "sheets", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [880, -240], "id": "8d2519dd-9e85-4fc3-a3b3-da9b1c84adb4", "name": "Output Type Switch"}, {"parameters": {"jsCode": "// 準備 Google Docs 內容 - 傳遞翻譯內容\nconsole.log('=== PREPARE DOCS CONTENT ===');\nconsole.log('Input from Output Type Switch:', JSON.stringify($json, null, 2));\n\n// 獲取翻譯內容\nlet finalTranslation = $json.finalTranslation || '';\nlet originalFileName = $json.originalFileName || '';\n\nconsole.log('📋 Input data check:');\nconsole.log('- finalTranslation:', finalTranslation ? `${finalTranslation.length} chars` : 'Missing');\nconsole.log('- originalFileName:', originalFileName || 'Missing');\n\n// 檢查翻譯內容\nif (!finalTranslation || finalTranslation.trim().length === 0) {\n  console.log('❌ No finalTranslation found, checking other fields...');\n  const possibleFields = ['finalTranslation', 'initialTranslation', 'content', 'text'];\n  for (const field of possibleFields) {\n    if ($json[field] && typeof $json[field] === 'string' && $json[field].trim().length > 0) {\n      finalTranslation = $json[field];\n      console.log(`✅ Found content in ${field}`);\n      break;\n    }\n  }\n}\n\nif (!finalTranslation || finalTranslation.trim().length === 0) {\n  throw new Error('無法獲取翻譯內容，請檢查翻譯節點執行狀態');\n}\n\n// 處理文件名\nif (!originalFileName || originalFileName === 'Unknown') {\n  originalFileName = 'Google文檔';\n}\noriginalFileName = originalFileName.replace(/\\.(doc|docx|txt|pdf)$/i, '');\nconst finalTitle = `${originalFileName}_翻譯`;\n\nconsole.log('📝 Preparing document creation:');\nconsole.log('- Title:', finalTitle);\nconsole.log('- Content length:', finalTranslation.length);\nconsole.log('- Content preview:', finalTranslation.substring(0, 100));\n\n// 返回完整數據，包含翻譯內容\nreturn {\n  title: finalTitle,\n  content: finalTranslation,\n  folderId: '1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg',\n  originalFileName: originalFileName,\n  finalTranslation: finalTranslation,\n  translationContent: finalTranslation\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [960, -300], "id": "6d83ade9-adee-4be2-92a0-0e072de016f7", "name": "Prepare Docs Content"}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/drive/v3/files", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  name: $json.title || 'Translated Document',\n  parents: ['1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg'],\n  mimeType: 'application/vnd.google-apps.document'\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1080, -300], "id": "84b406ea-1027-4351-87ff-3ec17bcea8c0", "name": "Create Google Doc via Drive API", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"method": "POST", "url": "={{ `https://docs.googleapis.com/v1/documents/${$json.documentId}:batchUpdate` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDocsOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  requests: [\n    {\n      insertText: {\n        location: {\n          index: 1\n        },\n        text: $json.content || '無法獲取翻譯內容'\n      }\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1400, -300], "id": "0f7aa49c-c055-4f2c-954e-cb80c7a0eee6", "name": "Add Content to Google Doc", "credentials": {"googleDocsOAuth2Api": {"id": "aKNvhMNpGwKLTvmj", "name": "Google Docs account"}}}, {"parameters": {"jsCode": "// 重建 Google Sheets 結構並替換翻譯內容\nconst translatedText = $json.finalTranslation;\nconst originalData = $json.originalData;\nlet originalFileName = $json.originalFileName;\n\nif (!originalData) {\n  throw new Error('原始 Sheets 數據缺失，無法重建文件。');\n}\n\n// 從多個可能的源獲取原始檔案名稱\nif (!originalFileName || originalFileName === 'Unknown' || originalFileName === 'Unknown Document') {\n  console.log('⚠️ Original filename not found for Sheets, checking upstream nodes...');\n  \n  // 嘗試從 Google Drive Trigger 獲取\n  if ($('Google Drive Trigger').length > 0) {\n    const triggerData = $('Google Drive Trigger').first().json;\n    if (triggerData && triggerData.name) {\n      originalFileName = triggerData.name;\n      console.log('✅ Found Sheets filename from Google Drive Trigger:', originalFileName);\n    }\n  }\n  \n  // 嘗試從 File Type Filter 獲取\n  if ((!originalFileName || originalFileName === 'Unknown') && $('File Type Filter').length > 0) {\n    const filterData = $('File Type Filter').first().json;\n    if (filterData && filterData.name) {\n      originalFileName = filterData.name;\n      console.log('✅ Found Sheets filename from File Type Filter:', originalFileName);\n    }\n  }\n}\n\n// 清理檔名：移除副檔名\nif (originalFileName && originalFileName !== 'Unknown') {\n  originalFileName = originalFileName.replace(/\\.(xls|xlsx|csv)$/i, '');\n  console.log('📝 Cleaned Sheets filename:', originalFileName);\n}\n\n// 最終檔名檢查\nif (!originalFileName || originalFileName === 'Unknown') {\n  originalFileName = 'Google試算表';\n  console.log('⚠️ Using fallback Sheets filename:', originalFileName);\n}\n\n// 將翻譯文本按行分割\nconst translatedLines = translatedText.split('\\n').filter(line => line.trim() !== '');\nlet lineIndex = 0;\n\nconst rebuiltData = [];\n\n// 遍歷原始數據的每一行\nfor (const originalRow of originalData) {\n  const newRow = {};\n  for (const key in originalRow) {\n    const originalValue = originalRow[key];\n    if (typeof originalValue === 'string' && originalValue.trim() !== '') {\n      if (lineIndex < translatedLines.length) {\n        newRow[key] = translatedLines[lineIndex];\n        lineIndex++;\n      } else {\n        newRow[key] = originalValue;\n      }\n    } else {\n      newRow[key] = originalValue;\n    }\n  }\n  rebuiltData.push(newRow);\n}\n\nconst finalFileName = `${originalFileName}_翻譯`;\nconsole.log('🏷️ Final Sheets filename:', finalFileName);\n\nreturn [\n  {\n    json: {\n      rebuiltData: rebuiltData,\n      fileName: finalFileName\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1080, -180], "id": "f71c10b0-6096-40ac-9d16-126f6c622043", "name": "Rebuild Sheets Structure"}, {"parameters": {"operation": "create"}, "type": "n8n-nodes-base.spreadsheetFile", "typeVersion": 2, "position": [1320, -180], "id": "f5bd794b-9c43-4b04-9ac2-813bfca68e16", "name": "Create Excel File"}, {"parameters": {"name": "={{ $('Rebuild Sheets Structure').length > 0 ? $('Rebuild Sheets Structure').first().json.fileName : 'Unknown_Sheet_translated' }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "mode": "id", "value": "1LADP6VD4ojtFyjuTNiNwLS3jB25tXG8X"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1520, -180], "id": "********-7a38-4e6b-b8b0-932d916d410a", "name": "Upload Translated Sheet", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Get the output from the \"Create Google Doc\" node (the current input)\nconst docInfo = $json;\nconst documentId = docInfo.id;\n\nif (!documentId) {\n  throw new Error('Could not find document ID from the \"Create Google Doc\" node.');\n}\n\n// Get the output from the \"Prepare Docs Content\" node\n// We use a node reference here because the content data was in the previous step\nconst prepNodeItems = $('Prepare Docs Content').all();\n\nif (!prepNodeItems || prepNodeItems.length === 0) {\n    throw new Error('Could not find output from the \"Prepare Docs Content\" node.');\n}\n\n// Find the content from the first item of that node's output\nconst translationContent = prepNodeItems[0].json.content;\n\nif (!translationContent) {\n  throw new Error('Could not find \"content\" in the output of \"Prepare Docs Content\" node.');\n}\n\n// Return the combined payload\nreturn {\n  documentId: documentId,\n  content: translationContent,\n  documentUrl: `https://docs.google.com/document/d/${documentId}/edit`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1200, -300], "id": "15d8036f-fac1-4282-96bc-45817fe27753", "name": "Prepare Final Payload"}], "pinData": {}, "connections": {"Google Drive Trigger": {"main": [[{"node": "File Type Filter", "type": "main", "index": 0}]]}, "File Type Filter": {"main": [[{"node": "Export Google Docs as Text", "type": "main", "index": 0}], [{"node": "Download Google Sheets as Excel", "type": "main", "index": 0}]]}, "Export Google Docs as Text": {"main": [[{"node": "Extract Docs Text", "type": "main", "index": 0}]]}, "Download Google Sheets as Excel": {"main": [[{"node": "Parse Sheets Content", "type": "main", "index": 0}]]}, "Extract Docs Text": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 0}]]}, "Parse Sheets Content": {"main": [[{"node": "Extract Sheets Text", "type": "main", "index": 0}]]}, "Extract Sheets Text": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 1}]]}, "Merge Extracted Content": {"main": [[{"node": "First Translation (<PERSON>)", "type": "main", "index": 0}]]}, "First Translation (Gemini)": {"main": [[{"node": "Process Gemini Response", "type": "main", "index": 0}]]}, "Process Gemini Response": {"main": [[{"node": "Reflective Translation (<PERSON>)", "type": "main", "index": 0}]]}, "Reflective Translation (Claude)": {"main": [[{"node": "Process Claude Response", "type": "main", "index": 0}]]}, "Process Claude Response": {"main": [[{"node": "Output Type Switch", "type": "main", "index": 0}]]}, "Output Type Switch": {"main": [[{"node": "Prepare Docs Content", "type": "main", "index": 0}], [{"node": "Rebuild Sheets Structure", "type": "main", "index": 0}]]}, "Rebuild Sheets Structure": {"main": [[{"node": "Create Excel File", "type": "main", "index": 0}]]}, "Create Excel File": {"main": [[{"node": "Upload Translated Sheet", "type": "main", "index": 0}]]}, "Prepare Docs Content": {"main": [[{"node": "Create Google Doc via Drive API", "type": "main", "index": 0}]]}, "Create Google Doc via Drive API": {"main": [[{"node": "Prepare Final Payload", "type": "main", "index": 0}]]}, "Prepare Final Payload": {"main": [[{"node": "Add Content to Google Doc", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "cfaaa85a-2b06-414c-9191-a3717ae27f97", "meta": {"templateCredsSetupCompleted": true, "instanceId": "cf8945d612793a3345978938d5d97bfc51695b35f0132e97cf82bfc6ce55e338"}, "id": "xTkG5CxPFcjTgxio", "tags": []}