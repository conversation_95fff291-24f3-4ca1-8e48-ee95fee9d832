// N8N 環境診斷腳本
// 在 N8N 的 Code 節點中運行此腳本來診斷問題

console.log('🔍 N8N 環境診斷開始...\n');

// 1. 檢查 Node.js 環境
console.log('📋 Node.js 環境信息:');
console.log(`  - Node.js 版本: ${process.version}`);
console.log(`  - 平台: ${process.platform}`);
console.log(`  - 架構: ${process.arch}`);
console.log(`  - 工作目錄: ${process.cwd()}`);
console.log(`  - 執行檔路徑: ${process.execPath}`);

// 2. 檢查模組路徑
console.log('\n📂 Node.js 模組搜尋路徑:');
require.main.paths.forEach((path, index) => {
  console.log(`  ${index + 1}. ${path}`);
});

// 3. 檢查環境變數
console.log('\n🔧 相關環境變數:');
console.log(`  - NODE_PATH: ${process.env.NODE_PATH || '未設定'}`);
console.log(`  - NPM_CONFIG_PREFIX: ${process.env.NPM_CONFIG_PREFIX || '未設定'}`);
console.log(`  - HOME: ${process.env.HOME || '未設定'}`);

// 4. 嘗試載入 mammoth
console.log('\n📄 測試 mammoth.js:');
try {
  const mammoth = require('mammoth');
  console.log('✅ mammoth.js 載入成功');
  console.log(`  - 類型: ${typeof mammoth}`);
  console.log(`  - 可用方法: ${Object.keys(mammoth).join(', ')}`);
  
  if (mammoth.extractRawText) {
    console.log('✅ extractRawText 方法可用');
  } else {
    console.log('❌ extractRawText 方法不可用');
  }
} catch (error) {
  console.log('❌ mammoth.js 載入失敗');
  console.log(`  - 錯誤類型: ${error.name}`);
  console.log(`  - 錯誤訊息: ${error.message}`);
  console.log(`  - 錯誤代碼: ${error.code || '無'}`);
}

// 5. 嘗試載入 xlsx
console.log('\n📊 測試 xlsx:');
try {
  const XLSX = require('xlsx');
  console.log('✅ xlsx 載入成功');
  console.log(`  - 類型: ${typeof XLSX}`);
  console.log(`  - 版本: ${XLSX.version || '未知'}`);
  
  if (XLSX.read) {
    console.log('✅ read 方法可用');
  } else {
    console.log('❌ read 方法不可用');
  }
  
  if (XLSX.utils && XLSX.utils.sheet_to_json) {
    console.log('✅ utils.sheet_to_json 方法可用');
  } else {
    console.log('❌ utils.sheet_to_json 方法不可用');
  }
} catch (error) {
  console.log('❌ xlsx 載入失敗');
  console.log(`  - 錯誤類型: ${error.name}`);
  console.log(`  - 錯誤訊息: ${error.message}`);
  console.log(`  - 錯誤代碼: ${error.code || '無'}`);
}

// 6. 檢查可用的模組
console.log('\n📦 嘗試列出已安裝的模組:');
try {
  const fs = require('fs');
  const path = require('path');
  
  // 檢查常見的 node_modules 位置
  const possiblePaths = [
    '/usr/local/lib/node_modules',
    '/usr/lib/node_modules',
    path.join(process.cwd(), 'node_modules'),
    path.join(process.env.HOME || '', 'node_modules'),
    path.join(process.env.HOME || '', '.npm-global', 'lib', 'node_modules')
  ];
  
  possiblePaths.forEach(modulePath => {
    try {
      if (fs.existsSync(modulePath)) {
        console.log(`  📁 ${modulePath}:`);
        const modules = fs.readdirSync(modulePath);
        const relevantModules = modules.filter(mod => 
          mod.includes('mammoth') || mod.includes('xlsx') || mod.includes('n8n')
        );
        if (relevantModules.length > 0) {
          relevantModules.forEach(mod => console.log(`    - ${mod}`));
        } else {
          console.log(`    (無相關模組)`);
        }
      }
    } catch (err) {
      console.log(`  ❌ 無法讀取 ${modulePath}: ${err.message}`);
    }
  });
} catch (error) {
  console.log(`❌ 檢查模組時發生錯誤: ${error.message}`);
}

// 7. 建議解決方案
console.log('\n💡 建議解決方案:');

let mammothOk = false;
let xlsxOk = false;

try {
  require('mammoth');
  mammothOk = true;
} catch {}

try {
  require('xlsx');
  xlsxOk = true;
} catch {}

if (!mammothOk || !xlsxOk) {
  console.log('根據診斷結果，建議執行以下步驟:');
  
  if (!mammothOk) {
    console.log('\n📄 mammoth.js 問題:');
    console.log('1. 確認安裝位置: npm list -g mammoth');
    console.log('2. 重新安裝: npm install -g mammoth');
    console.log('3. 或在 N8N 工作目錄安裝: npm install mammoth');
  }
  
  if (!xlsxOk) {
    console.log('\n📊 xlsx 問題:');
    console.log('1. 確認安裝位置: npm list -g xlsx');
    console.log('2. 重新安裝: npm install -g xlsx');
    console.log('3. 或在 N8N 工作目錄安裝: npm install xlsx');
  }
  
  console.log('\n🔄 安裝後請重啟 N8N 服務');
} else {
  console.log('✅ 所有庫都可正常載入！');
}

return {
  status: 'diagnosis_complete',
  mammoth_available: mammothOk,
  xlsx_available: xlsxOk,
  node_version: process.version,
  working_directory: process.cwd()
};
