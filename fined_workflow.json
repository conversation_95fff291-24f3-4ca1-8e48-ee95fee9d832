{"name": "My workflow 4", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "https://drive.google.com/drive/u/2/folders/1LADP6VD4ojtFyjuTNiNwLS3jB25tXG8X", "mode": "url"}, "event": "fileUpdated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-1680, -140], "id": "4ae9e52c-a3df-4ea4-8927-325150bcfa5e", "name": "Google Drive Trigger", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.webViewLink }}", "mode": "url"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-1380, -140], "id": "202f8f03-c09b-42bd-820d-dcdbac3b0afc", "name": "Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/speech-to-text", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "xi-api-key"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "model_id", "value": "scribe_v1"}, {"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "data"}, {"name": "additional_formats", "value": "[{\"format\":\"pdf\"}]"}, {"name": "diarize", "value": "true"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-740, -160], "id": "0cf2e206-1ac9-4b14-9681-0d56729570e5", "name": "HTTP Request"}, {"parameters": {"method": "POST", "url": "https://translation.googleapis.com/language/translate/v2?key=", "sendQuery": true, "queryParameters": {"parameters": [{}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json; charset=utf-8"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "q", "value": "={{ $json.text }}"}, {"name": "source", "value": "en"}, {"name": "target", "value": "fr"}, {"name": "format", "value": "text"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-120, -160], "id": "b729f16f-845d-4b90-a9fc-9b4766ae9b12", "name": "HTTP Request2"}, {"parameters": {"folderId": "15mz8HluRZFbGjYgFGlyxuJraAQHC--ts", "title": "={{ $json.output }}"}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [460, -160], "id": "372730c5-6b80-4a30-b02e-f1651fcd823c", "name": "Google Docs"}, {"parameters": {"operation": "update", "documentURL": "={{ $json.id }}", "actionsUi": {"actionFields": [{"action": "insert", "text": "={{ $('HTTP Request2').item.json.data.translations[0].translatedText }}"}]}}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [740, -160], "id": "ee702493-9384-47e9-a1bf-f9fba1c43902", "name": "Google Docs1"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.fileExtension }}", "rightValue": "mp4", "operator": {"type": "string", "operation": "equals"}, "id": "075a29bb-26e8-4d9d-8a3a-3718199ff9cc"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b5703c09-b09f-4fe5-a1c1-c82de4fe23c0", "leftValue": "={{ $json.fileExtension }}", "rightValue": "pdf", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-1160, -140], "id": "d33d3b33-b6e3-4a22-ae10-51eb486fee83", "name": "Switch"}, {"parameters": {"operation": "pdf", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-740, 60], "id": "edc581f1-74f1-4aff-bb31-965fdc85222a", "name": "Extract from File"}, {"parameters": {"assignments": {"assignments": [{"id": "0bca3111-7708-46f2-b0ba-f227f7600fe8", "name": "text", "value": "={{ $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-400, -160], "id": "55828a24-b264-426e-94ed-a3234ee7e9fd", "name": "Edit Fields1"}, {"parameters": {"promptType": "define", "text": "={{ $json.data.translations[0].translatedText }}", "options": {"systemMessage": "=You are a helpful assistant that generates a 5 word title based on this text:  {{ $json.data.translations[0].translatedText }}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [80, -160], "id": "32c71fa0-e92a-401a-906a-c0c082322ba0", "name": "AI Agent"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [100, 140], "id": "15bf931f-ae8b-4e7f-8519-910c26e1e41a", "name": "Google Gemini Chat Model"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('HTTP Request2').item.json.data.translations[0].translatedText }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [200, 140], "id": "7ef2c711-63ad-409d-94a6-51af87efe8bb", "name": "Simple Memory"}], "pinData": {}, "connections": {"Google Drive Trigger": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "HTTP Request2": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Google Docs": {"main": [[{"node": "Google Docs1", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}], [{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "HTTP Request2", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Google Docs", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner", "executionTimeout": -1}, "versionId": "94467f58-bf81-4b14-b0e1-46a3cd9f62f3", "meta": {"templateCredsSetupCompleted": true, "instanceId": "cf8945d612793a3345978938d5d97bfc51695b35f0132e97cf82bfc6ce55e338"}, "id": "xTkG5CxPFcjTgxio", "tags": []}